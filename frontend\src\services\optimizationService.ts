/**
 * Optimization Service
 * 
 * This service provides API integration for the architecture optimization feature,
 * replicating the PyQt5 MapleGUI "Optimize Architecture" functionality.
 */

import api from './api'
import type { ArchitectureNode, ArchitectureEdge } from '@/types/architecture'

// Request Types
export interface OptimizationRequest {
  target_service: string
  throughput: string
  latency_requirement: string
  memory_requirement: string
  current_memory: string
  current_cores: string
  unit: string
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
}

export interface ServiceAlternativesRequest {
  target_service: string
}

// Response Types
export interface OptimizationRecommendation {
  action: string
  current_service: string
  recommended_service: string
  recommendation_text: string
  change_type: string
  justification: string
}

export interface OptimizationResponse {
  status: string
  target_service: string
  current_configuration: {
    memory: string
    cores: string
  }
  alternatives_analyzed: number
  recommendation: OptimizationRecommendation
  optimization_results?: any
  error?: string
}

export interface ServiceAlternativesResponse {
  status: string
  target_service: string
  alternatives: string[]
  error?: string
}

export interface OptimizableServicesResponse {
  status: string
  services: {
    compute: string[]
    storage: string[]
    database: string[]
    cache: string[]
    analytics: string[]
  }
  total_services: number
}

class OptimizationService {
  private baseUrl = '/optimization'  // Fixed: API router already includes /api/v1 prefix

  /**
   * Test if optimization API is accessible
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('Testing optimization API connection...')
      const response = await api.get(`${this.baseUrl}/services`)
      console.log('Connection test response:', response)
      return response.status === 200
    } catch (error: any) {
      console.error('Optimization API connection test failed:', error)
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      })
      return false
    }
  }

  /**
   * Optimize architecture for a specific service
   */
  async optimizeArchitecture(request: OptimizationRequest): Promise<OptimizationResponse> {
    try {
      console.log('Starting architecture optimization:', request)
      
      const response = await api.post<OptimizationResponse>(`${this.baseUrl}/optimize`, request)

      // The response data is in response.data
      const data = response.data

      if (data.status !== 'success') {
        throw new Error(data.error || 'Optimization failed')
      }

      console.log('Optimization completed:', data)
      return data
      
    } catch (error: any) {
      console.error('Error in architecture optimization:', error)
      
      // Handle API errors
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail)
      }
      
      throw new Error(error.message || 'Failed to optimize architecture')
    }
  }

  /**
   * Get alternative services for a given target service
   */
  async getServiceAlternatives(request: ServiceAlternativesRequest): Promise<ServiceAlternativesResponse> {
    try {
      console.log('Getting service alternatives:', request)
      
      const response = await api.post<ServiceAlternativesResponse>(`${this.baseUrl}/alternatives`, request)

      // The response data is in response.data
      const data = response.data

      if (data.status !== 'success') {
        throw new Error(data.error || 'Failed to get alternatives')
      }

      console.log('Service alternatives retrieved:', data)
      return data
      
    } catch (error: any) {
      console.error('Error getting service alternatives:', error)
      
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail)
      }
      
      throw new Error(error.message || 'Failed to get service alternatives')
    }
  }

  /**
   * Get list of services that can be optimized
   */
  async getOptimizableServices(): Promise<OptimizableServicesResponse> {
    try {
      console.log('Getting optimizable services')

      const response = await api.get<OptimizableServicesResponse>(`${this.baseUrl}/services`)

      // The response data is in response.data
      const data = response.data
      console.log('Raw API response:', response)
      console.log('Response data:', data)

      if (data.status !== 'success') {
        throw new Error('Failed to get optimizable services')
      }

      console.log('Optimizable services retrieved:', data)
      return data

    } catch (error: any) {
      console.error('Error getting optimizable services:', error)

      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail)
      }

      throw new Error(error.message || 'Failed to get optimizable services')
    }
  }

  /**
   * Validate optimization request
   */
  validateOptimizationRequest(request: OptimizationRequest): string[] {
    const errors: string[] = []

    if (!request.target_service) {
      errors.push('Target service is required')
    }

    if (!request.throughput || isNaN(Number(request.throughput)) || Number(request.throughput) <= 0) {
      errors.push('Valid throughput value is required')
    }

    if (!request.latency_requirement || isNaN(Number(request.latency_requirement)) || Number(request.latency_requirement) <= 0) {
      errors.push('Valid latency requirement is required')
    }

    if (!request.memory_requirement || isNaN(Number(request.memory_requirement)) || Number(request.memory_requirement) <= 0) {
      errors.push('Valid memory requirement is required')
    }

    if (!request.current_memory || isNaN(Number(request.current_memory)) || Number(request.current_memory) <= 0) {
      errors.push('Valid current memory value is required')
    }

    if (!request.current_cores || isNaN(Number(request.current_cores)) || Number(request.current_cores) <= 0) {
      errors.push('Valid current cores value is required')
    }

    if (!request.unit || !['second', 'minute', 'hour'].includes(request.unit)) {
      errors.push('Valid time unit is required')
    }

    return errors
  }

  /**
   * Format optimization results for display
   */
  formatOptimizationResults(result: OptimizationResponse): {
    summary: string
    details: string[]
    actionRequired: boolean
  } {
    const recommendation = result.recommendation
    const actionRequired = recommendation.action !== 'keep_current'
    
    let summary = ''
    const details: string[] = []

    switch (recommendation.action) {
      case 'keep_current':
        summary = `Current ${recommendation.current_service} configuration is optimal`
        details.push('No changes needed')
        details.push(recommendation.justification)
        break
        
      case 'optimize_configuration':
        summary = `Optimize ${recommendation.current_service} configuration`
        details.push(`Current: ${result.current_configuration.memory} MB, ${result.current_configuration.cores} cores`)
        details.push(`Recommendation: ${recommendation.recommendation_text}`)
        details.push(recommendation.justification)
        break
        
      case 'replace_service':
        summary = `Replace ${recommendation.current_service} with ${recommendation.recommended_service}`
        details.push(`Current: ${recommendation.current_service}`)
        details.push(`Recommended: ${recommendation.recommended_service}`)
        details.push(recommendation.justification)
        break
        
      default:
        summary = 'Optimization analysis completed'
        details.push(recommendation.recommendation_text)
        details.push(recommendation.justification)
    }

    return {
      summary,
      details,
      actionRequired
    }
  }

  /**
   * Get optimization action display text
   */
  getActionDisplayText(action: string): string {
    switch (action) {
      case 'keep_current':
        return 'Keep Current Configuration'
      case 'optimize_configuration':
        return 'Optimize Configuration'
      case 'replace_service':
        return 'Replace Service'
      default:
        return 'Unknown Action'
    }
  }

  /**
   * Get optimization action color
   */
  getActionColor(action: string): string {
    switch (action) {
      case 'keep_current':
        return 'green'
      case 'optimize_configuration':
        return 'blue'
      case 'replace_service':
        return 'orange'
      default:
        return 'gray'
    }
  }

  /**
   * Extract service from architecture nodes
   */
  extractServicesFromNodes(nodes: ArchitectureNode[]): string[] {
    const services: string[] = []
    
    nodes.forEach(node => {
      const serviceName = node.data?.service?.name || node.data?.label
      if (serviceName && !serviceName.includes('User') && !services.includes(serviceName)) {
        services.push(serviceName)
      }
    })
    
    return services
  }

  /**
   * Debug endpoint to test node processing
   */
  async debugNodes(request: OptimizationRequest): Promise<any> {
    try {
      console.log('Debugging nodes with backend:', request)

      const response = await api.post<any>(`${this.baseUrl}/debug-nodes`, request)
      const data = response.data

      console.log('Debug response:', data)
      return data

    } catch (error: any) {
      console.error('Error in debug nodes:', error)
      throw new Error(error.message || 'Failed to debug nodes')
    }
  }

  /**
   * Check if service is optimizable
   */
  async isServiceOptimizable(serviceName: string): Promise<boolean> {
    try {
      const services = await this.getOptimizableServices()
      const allServices = Object.values(services.services).flat()
      return allServices.includes(serviceName)
    } catch (error) {
      console.error('Error checking if service is optimizable:', error)
      return false
    }
  }
}

// Create and export singleton instance
export const optimizationApi = new OptimizationService()
export default optimizationApi

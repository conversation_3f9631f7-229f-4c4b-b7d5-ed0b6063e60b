/**
 * Architecture Chat Component
 * 
 * AI-powered chat interface for architecture analysis, optimization,
 * and general questions about cloud architectures.
 */

import React, { useState, useRef, useEffect } from 'react'
import { Send, Bot, User, Loader2, MessageSquare, Lightbulb, DollarSign, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { architectureChatApi } from '@/services/architectureChatService'
import type { ArchitectureNode, ArchitectureEdge } from '@/types/architecture'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  toolsUsed?: string[]
}

interface ArchitectureChatProps {
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
  isVisible: boolean
  onClose: () => void
}

export default function ArchitectureChat({
  nodes,
  edges,
  isVisible,
  onClose
}: ArchitectureChatProps) {
  const { toast } = useToast()
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [conversationId, setConversationId] = useState<string>('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Focus input when chat becomes visible
  useEffect(() => {
    if (isVisible) {
      inputRef.current?.focus()
    }
  }, [isVisible])

  // Initialize conversation with welcome message
  useEffect(() => {
    if (isVisible && messages.length === 0) {
      const welcomeMessage: Message = {
        id: 'welcome',
        role: 'assistant',
        content: `Hello! I'm your AI architecture assistant. I can help you with:

🏗️ **Architecture Analysis** - Analyze your current architecture and identify patterns
💰 **Cost Optimization** - Calculate costs and suggest optimizations  
🔍 **Best Practices** - Provide recommendations based on AWS best practices
🌐 **Web Search** - Find up-to-date information about cloud services
⚡ **Performance** - Suggest performance improvements

${nodes.length > 0 ? `I can see you have ${nodes.length} services in your current architecture. Feel free to ask me anything about it!` : 'Start by asking me a question about cloud architecture or upload an architecture to analyze.'}`,
        timestamp: new Date().toISOString(),
        toolsUsed: []
      }
      setMessages([welcomeMessage])
    }
  }, [isVisible, nodes.length])

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    try {
      // Prepare architecture data
      const architectureData = nodes.length > 0 ? { nodes, edges } : undefined

      const response = await architectureChatApi.chatWithArchitecture({
        user_query: inputMessage,
        architecture_data: architectureData,
        conversation_id: conversationId
      })

      if (response.status === 'success') {
        const assistantMessage: Message = {
          id: `assistant-${Date.now()}`,
          role: 'assistant',
          content: response.response,
          timestamp: response.timestamp,
          toolsUsed: response.tools_used
        }

        setMessages(prev => [...prev, assistantMessage])
        setConversationId(response.conversation_id)
      } else {
        throw new Error(response.error || 'Failed to get response')
      }
    } catch (error) {
      console.error('Chat error:', error)
      toast({
        title: "Chat Error",
        description: "Failed to get AI response. Please try again.",
        variant: "destructive"
      })

      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: 'I apologize, but I encountered an error. Please try asking your question again.',
        timestamp: new Date().toISOString()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const getQuickActions = () => [
    {
      label: "Analyze Architecture",
      icon: MessageSquare,
      action: () => setInputMessage("Please analyze my current architecture and provide insights."),
      disabled: nodes.length === 0
    },
    {
      label: "Cost Analysis",
      icon: DollarSign,
      action: () => setInputMessage("Calculate the cost of my architecture and suggest optimizations."),
      disabled: nodes.length === 0
    },
    {
      label: "Optimization Tips",
      icon: Lightbulb,
      action: () => setInputMessage("What are some ways I can optimize my architecture for better performance and cost?"),
      disabled: nodes.length === 0
    },
    {
      label: "Best Practices",
      icon: Search,
      action: () => setInputMessage("What are the current AWS best practices for cloud architecture design?"),
      disabled: false
    }
  ]

  const formatMessage = (content: string) => {
    // Simple formatting for better readability
    return content
      .split('\n')
      .map((line, index) => (
        <div key={index} className={line.trim() === '' ? 'h-2' : ''}>
          {line.includes('**') ? (
            <div dangerouslySetInnerHTML={{
              __html: line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            }} />
          ) : (
            line
          )}
        </div>
      ))
  }

  const getToolBadgeColor = (tool: string) => {
    switch (tool) {
      case 'web_search': return 'bg-blue-100 text-blue-800'
      case 'analyze_architecture': return 'bg-green-100 text-green-800'
      case 'calculate_cost': return 'bg-yellow-100 text-yellow-800'
      case 'suggest_optimizations': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (!isVisible) return null

  return (
    <Card className="fixed inset-4 z-50 flex flex-col max-h-[90vh]">
      <CardHeader className="flex-shrink-0 pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-blue-600" />
            Architecture AI Assistant
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            ✕
          </Button>
        </div>
        {nodes.length > 0 && (
          <div className="text-sm text-gray-600">
            Analyzing architecture with {nodes.length} services and {edges.length} connections
          </div>
        )}
      </CardHeader>

      <CardContent className="flex-1 flex flex-col min-h-0 p-4">
        {/* Messages Area */}
        <ScrollArea className="flex-1 mb-4 border rounded-lg p-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex gap-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                  <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                    message.role === 'user' 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {message.role === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                  </div>
                  <div className={`rounded-lg p-3 ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    <div className="text-sm whitespace-pre-wrap">
                      {formatMessage(message.content)}
                    </div>
                    {message.toolsUsed && message.toolsUsed.length > 0 && (
                      <div className="flex gap-1 mt-2 flex-wrap">
                        {message.toolsUsed.map((tool, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className={`text-xs ${getToolBadgeColor(tool)}`}
                          >
                            {tool.replace('_', ' ')}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex gap-3 justify-start">
                <div className="flex gap-3 max-w-[80%]">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center">
                    <Bot className="h-4 w-4" />
                  </div>
                  <div className="bg-gray-100 text-gray-900 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm">Thinking...</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Quick Actions */}
        {messages.length <= 1 && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700 mb-2">Quick Actions:</div>
            <div className="grid grid-cols-2 gap-2">
              {getQuickActions().map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="justify-start h-auto p-2"
                  onClick={action.action}
                  disabled={action.disabled}
                >
                  <action.icon className="h-4 w-4 mr-2" />
                  <span className="text-xs">{action.label}</span>
                </Button>
              ))}
            </div>
            <Separator className="mt-4" />
          </div>
        )}

        {/* Input Area */}
        <div className="flex gap-2">
          <Input
            ref={inputRef}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me about your architecture..."
            disabled={isLoading}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            size="icon"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

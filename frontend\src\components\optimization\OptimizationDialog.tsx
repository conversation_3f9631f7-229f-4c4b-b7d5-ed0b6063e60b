import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { AlertCircle, CheckCircle, TrendingDown, TrendingUp, Zap } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { optimizationApi } from '@/services/optimizationService'
import type { ArchitectureNode, ArchitectureEdge } from '@/types/architecture'

interface OptimizationD<PERSON>ogProps {
  isOpen: boolean
  onClose: () => void
  selectedService?: string
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
  onOptimizationApplied?: (recommendation: any) => void
}

interface OptimizationRequest {
  target_service: string
  throughput: string
  latency_requirement: string
  memory_requirement: string
  current_memory: string
  current_cores: string
  unit: string
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
}

interface OptimizationRecommendation {
  action: string
  current_service: string
  recommended_service: string
  recommendation_text: string
  change_type: string
  justification: string
}

interface OptimizationResult {
  status: string
  target_service: string
  current_configuration: {
    memory: string
    cores: string
  }
  alternatives_analyzed: number
  recommendation: OptimizationRecommendation
  optimization_results?: any
  error?: string
}

export default function OptimizationDialog({
  isOpen,
  onClose,
  selectedService,
  nodes,
  edges,
  onOptimizationApplied
}: OptimizationDialogProps) {
  const { toast } = useToast()
  const [step, setStep] = useState<'input' | 'processing' | 'results'>('input')
  const [progress, setProgress] = useState(0)
  const [optimizationRequest, setOptimizationRequest] = useState<OptimizationRequest>({
    target_service: selectedService || '',
    throughput: '1000',
    latency_requirement: '10',
    memory_requirement: '1024',
    current_memory: '1024',
    current_cores: '2',
    unit: 'second',
    nodes,
    edges
  })
  const [optimizationResult, setOptimizationResult] = useState<OptimizationResult | null>(null)
  const [availableServices, setAvailableServices] = useState<string[]>([])
  const [serviceCategories, setServiceCategories] = useState<{[key: string]: string[]}>({})
  const [isLoadingServices, setIsLoadingServices] = useState(false)

  // Load available services on mount
  useEffect(() => {
    const loadServices = async () => {
      setIsLoadingServices(true)
      try {
        console.log('Loading optimizable services...')
        const response = await optimizationApi.getOptimizableServices()
        console.log('Services response:', response)

        if (response.status === 'success') {
          const allServices = Object.values(response.services).flat()
          console.log('All services:', allServices)
          setAvailableServices(allServices)
          setServiceCategories(response.services)

          toast({
            title: "Services Loaded",
            description: `Loaded ${allServices.length} optimizable services`,
          })
        } else {
          console.error('Failed to load services:', response)
          loadFallbackServices()
        }
      } catch (error) {
        console.error('Error loading services:', error)
        loadFallbackServices()
      } finally {
        setIsLoadingServices(false)
      }
    }

    const loadFallbackServices = () => {
      console.log('Loading fallback services...')

      // Comprehensive fallback list of AWS services
      const fallbackCategories = {
        'compute': [
          'Amazon EC2',
          'AWS Lambda',
          'Amazon ECS',
          'AWS Fargate',
          'Amazon Lightsail'
        ],
        'storage': [
          'Amazon S3',
          'Amazon S3 Glacier',
          'Amazon EFS',
          'Amazon EBS'
        ],
        'database': [
          'Amazon DynamoDB',
          'Amazon DynamoDB Accelerator (DAX)',
          'Amazon RDS',
          'Amazon Aurora',
          'Amazon DocumentDB'
        ],
        'cache': [
          'Amazon ElastiCache',
          'Amazon MemoryDB'
        ],
        'networking': [
          'Amazon VPC',
          'Amazon CloudFront',
          'Amazon API Gateway',
          'Amazon Route 53'
        ],
        'analytics': [
          'Amazon Athena',
          'Amazon QuickSight',
          'Amazon Redshift',
          'Amazon EMR'
        ],
        'messaging': [
          'Amazon SQS',
          'Amazon SNS',
          'Amazon Kinesis'
        ],
        'ml_ai': [
          'Amazon SageMaker',
          'Amazon Rekognition',
          'Amazon Comprehend'
        ],
        'monitoring': [
          'Amazon CloudWatch',
          'AWS X-Ray',
          'AWS CloudTrail'
        ],
        'security': [
          'AWS WAF',
          'Amazon Cognito',
          'AWS IAM',
          'AWS KMS'
        ]
      }

      const allServices = Object.values(fallbackCategories).flat()
      setAvailableServices(allServices)
      setServiceCategories(fallbackCategories)

      toast({
        title: "Services Loaded (Offline)",
        description: `Loaded ${allServices.length} services from local cache`,
        variant: "default"
      })
    }

    if (isOpen) {
      loadServices()
    }
  }, [isOpen, toast])

  // Update target service when selectedService changes
  useEffect(() => {
    if (selectedService) {
      setOptimizationRequest(prev => ({
        ...prev,
        target_service: selectedService
      }))
    }
  }, [selectedService])

  const handleInputChange = (field: keyof OptimizationRequest, value: string) => {
    setOptimizationRequest(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleOptimize = async () => {
    try {
      setStep('processing')
      setProgress(0)

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 500)

      const result = await optimizationApi.optimizeArchitecture(optimizationRequest)
      
      clearInterval(progressInterval)
      setProgress(100)
      
      setTimeout(() => {
        setOptimizationResult(result)
        setStep('results')
      }, 500)

    } catch (error: any) {
      console.error('Optimization error:', error)
      toast({
        title: "Optimization Failed",
        description: error.message || "An error occurred during optimization",
        variant: "destructive"
      })
      setStep('input')
    }
  }

  const handleApplyRecommendation = () => {
    if (optimizationResult && onOptimizationApplied) {
      onOptimizationApplied(optimizationResult.recommendation)
      toast({
        title: "Optimization Applied",
        description: "Architecture has been updated with the optimization recommendation",
      })
      onClose()
    }
  }

  const handleReset = () => {
    setStep('input')
    setProgress(0)
    setOptimizationResult(null)
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'keep_current':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'optimize_configuration':
        return <Zap className="h-5 w-5 text-blue-500" />
      case 'replace_service':
        return <TrendingUp className="h-5 w-5 text-orange-500" />
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />
    }
  }

  const getActionColor = (action: string) => {
    switch (action) {
      case 'keep_current':
        return 'bg-green-100 text-green-800'
      case 'optimize_configuration':
        return 'bg-blue-100 text-blue-800'
      case 'replace_service':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Optimize Architecture
          </DialogTitle>
        </DialogHeader>

        {step === 'input' && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="target_service">Target Service</Label>
                <Select
                  value={optimizationRequest.target_service}
                  onValueChange={(value) => handleInputChange('target_service', value)}
                  disabled={isLoadingServices}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={
                      isLoadingServices
                        ? "Loading services..."
                        : availableServices.length > 0
                          ? "Select service to optimize"
                          : "No services available"
                    } />
                  </SelectTrigger>
                  <SelectContent className="max-h-[300px]">
                    {Object.keys(serviceCategories).length > 0 ? (
                      // Show services grouped by category
                      Object.entries(serviceCategories).map(([category, services]) => (
                        <div key={category}>
                          <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                            {category.replace('_', ' ')}
                          </div>
                          {services.map((service) => (
                            <SelectItem key={service} value={service} className="pl-4">
                              {service}
                            </SelectItem>
                          ))}
                        </div>
                      ))
                    ) : (
                      // Fallback to flat list
                      availableServices.map((service) => (
                        <SelectItem key={service} value={service}>
                          {service}
                        </SelectItem>
                      ))
                    )}

                    {availableServices.length === 0 && !isLoadingServices && (
                      <div className="px-2 py-4 text-center text-gray-500">
                        No services available for optimization
                      </div>
                    )}
                  </SelectContent>
                </Select>

                {availableServices.length > 0 && (
                  <p className="text-xs text-gray-500">
                    {availableServices.length} services available for optimization
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="unit">Time Unit</Label>
                <Select
                  value={optimizationRequest.unit}
                  onValueChange={(value) => handleInputChange('unit', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="second">Per Second</SelectItem>
                    <SelectItem value="minute">Per Minute</SelectItem>
                    <SelectItem value="hour">Per Hour</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="throughput">Throughput</Label>
                <Input
                  id="throughput"
                  type="number"
                  value={optimizationRequest.throughput}
                  onChange={(e) => handleInputChange('throughput', e.target.value)}
                  placeholder="1000"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="latency_requirement">Max Latency (seconds)</Label>
                <Input
                  id="latency_requirement"
                  type="number"
                  step="0.1"
                  value={optimizationRequest.latency_requirement}
                  onChange={(e) => handleInputChange('latency_requirement', e.target.value)}
                  placeholder="10"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="memory_requirement">Required Memory (MB)</Label>
                <Input
                  id="memory_requirement"
                  type="number"
                  value={optimizationRequest.memory_requirement}
                  onChange={(e) => handleInputChange('memory_requirement', e.target.value)}
                  placeholder="1024"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="current_memory">Current Memory (MB)</Label>
                <Input
                  id="current_memory"
                  type="number"
                  value={optimizationRequest.current_memory}
                  onChange={(e) => handleInputChange('current_memory', e.target.value)}
                  placeholder="1024"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="current_cores">Current CPU Cores</Label>
                <Input
                  id="current_cores"
                  type="number"
                  value={optimizationRequest.current_cores}
                  onChange={(e) => handleInputChange('current_cores', e.target.value)}
                  placeholder="2"
                />
              </div>
            </div>
          </div>
        )}

        {step === 'processing' && (
          <div className="space-y-6 py-8">
            <div className="text-center">
              <Zap className="h-12 w-12 mx-auto mb-4 text-blue-500 animate-pulse" />
              <h3 className="text-lg font-semibold mb-2">Optimizing Architecture</h3>
              <p className="text-gray-600 mb-4">
                Analyzing {optimizationRequest.target_service} and finding optimal configurations...
              </p>
              <Progress value={progress} className="w-full max-w-md mx-auto" />
              <p className="text-sm text-gray-500 mt-2">{progress}% complete</p>
            </div>
          </div>
        )}

        {step === 'results' && optimizationResult && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getActionIcon(optimizationResult.recommendation.action)}
                  Optimization Recommendation
                </CardTitle>
                <CardDescription>
                  Analysis of {optimizationResult.target_service} with {optimizationResult.alternatives_analyzed} alternatives
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge className={getActionColor(optimizationResult.recommendation.action)}>
                    {optimizationResult.recommendation.action.replace('_', ' ').toUpperCase()}
                  </Badge>
                  <span className="text-sm text-gray-600">
                    {optimizationResult.recommendation.change_type.replace('_', ' ')}
                  </span>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {optimizationResult.recommendation.recommendation_text}
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Current Service</Label>
                    <p className="text-sm">{optimizationResult.recommendation.current_service}</p>
                    <p className="text-xs text-gray-500">
                      Memory: {optimizationResult.current_configuration.memory} MB, 
                      Cores: {optimizationResult.current_configuration.cores}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Recommended Service</Label>
                    <p className="text-sm">{optimizationResult.recommendation.recommended_service}</p>
                    <p className="text-xs text-gray-500">
                      {optimizationResult.recommendation.justification}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <DialogFooter>
          {step === 'input' && (
            <>
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                onClick={handleOptimize}
                disabled={!optimizationRequest.target_service}
              >
                Start Optimization
              </Button>
            </>
          )}

          {step === 'processing' && (
            <Button variant="outline" onClick={() => setStep('input')}>
              Cancel
            </Button>
          )}

          {step === 'results' && (
            <>
              <Button variant="outline" onClick={handleReset}>
                Run Another Optimization
              </Button>
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              {optimizationResult?.recommendation.action !== 'keep_current' && (
                <Button onClick={handleApplyRecommendation}>
                  Apply Recommendation
                </Button>
              )}
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

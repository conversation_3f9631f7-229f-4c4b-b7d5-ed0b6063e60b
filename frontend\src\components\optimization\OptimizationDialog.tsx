import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { AlertCircle, CheckCircle, TrendingDown, TrendingUp, Zap } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { optimizationApi } from '@/services/optimizationService'
import type { ArchitectureNode, ArchitectureEdge } from '@/types/architecture'

interface OptimizationD<PERSON>ogProps {
  isOpen: boolean
  onClose: () => void
  selectedService?: string
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
  onOptimizationApplied?: (recommendation: any) => void
}

interface OptimizationRequest {
  target_service: string
  throughput: string
  latency_requirement: string
  memory_requirement: string
  current_memory: string
  current_cores: string
  unit: string
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
}

interface OptimizationRecommendation {
  action: string
  current_service: string
  recommended_service: string
  recommendation_text: string
  change_type: string
  justification: string
}

interface OptimizationResult {
  status: string
  target_service: string
  current_configuration: {
    memory: string
    cores: string
  }
  alternatives_analyzed: number
  recommendation: OptimizationRecommendation
  optimization_results?: any
  error?: string
}

export default function OptimizationDialog({
  isOpen,
  onClose,
  selectedService,
  nodes,
  edges,
  onOptimizationApplied
}: OptimizationDialogProps) {
  const { toast } = useToast()
  const [step, setStep] = useState<'input' | 'processing' | 'results'>('input')
  const [progress, setProgress] = useState(0)
  const [optimizationRequest, setOptimizationRequest] = useState<OptimizationRequest>({
    target_service: selectedService || '',
    throughput: '1000',
    latency_requirement: '10',
    memory_requirement: '1024',
    current_memory: '1024',
    current_cores: '2',
    unit: 'second',
    nodes,
    edges
  })
  const [optimizationResult, setOptimizationResult] = useState<OptimizationResult | null>(null)
  const [availableServices, setAvailableServices] = useState<string[]>([])
  const [serviceCategories, setServiceCategories] = useState<{[key: string]: string[]}>({})
  const [isLoadingServices, setIsLoadingServices] = useState(false)

  // Load services from current architecture and test API connection
  useEffect(() => {
    const loadServicesFromArchitecture = async () => {
      setIsLoadingServices(true)

      // Test API connection first
      const isApiConnected = await optimizationApi.testConnection()
      console.log('Optimization API connection status:', isApiConnected)

      if (!isApiConnected) {
        toast({
          title: "API Connection Issue",
          description: "Cannot connect to optimization service. Please check if the backend is running.",
          variant: "destructive"
        })
      }

      try {
        console.log('Loading services from current architecture...')
        console.log('Architecture nodes:', nodes)

        // Extract services from current architecture nodes
        const architectureServices: string[] = []
        const serviceCategories: {[key: string]: string[]} = {
          'current_architecture': []
        }

        nodes.forEach((node, index) => {
          const nodeData = node.data
          let serviceName = ''

          // Extract service name from different node data structures (same logic as backend)
          if (nodeData?.service?.name) {
            serviceName = nodeData.service.name
          } else if (nodeData?.label) {
            serviceName = nodeData.label
          } else if (nodeData?.name) {
            serviceName = nodeData.name
          }

          console.log(`Node ${index}:`, {
            id: node.id,
            nodeData,
            extractedServiceName: serviceName
          })

          // Filter out User nodes and empty services
          if (serviceName && !serviceName.includes('User') && serviceName.trim() !== '') {
            const cleanServiceName = serviceName.trim()
            if (!architectureServices.includes(cleanServiceName)) {
              architectureServices.push(cleanServiceName)
              serviceCategories.current_architecture.push(cleanServiceName)
              console.log(`Added service: "${cleanServiceName}"`)
            }
          }
        })

        console.log('Services found in architecture:', architectureServices)

        if (architectureServices.length > 0) {
          setAvailableServices(architectureServices)
          setServiceCategories(serviceCategories)

          toast({
            title: "Services Loaded",
            description: `Found ${architectureServices.length} services in current architecture`,
          })
        } else {
          // No services in architecture, show message
          setAvailableServices([])
          setServiceCategories({})

          toast({
            title: "No Services Found",
            description: "No optimizable services found in current architecture. Please add services to your architecture first.",
            variant: "destructive"
          })
        }
      } catch (error) {
        console.error('Error loading services from architecture:', error)
        setAvailableServices([])
        setServiceCategories({})

        toast({
          title: "Error Loading Services",
          description: "Failed to load services from architecture",
          variant: "destructive"
        })
      } finally {
        setIsLoadingServices(false)
      }
    }

    if (isOpen) {
      loadServicesFromArchitecture()
    }
  }, [isOpen, nodes, toast])

  // Update target service when selectedService changes
  useEffect(() => {
    if (selectedService) {
      setOptimizationRequest(prev => ({
        ...prev,
        target_service: selectedService
      }))
    }
  }, [selectedService])

  const handleInputChange = (field: keyof OptimizationRequest, value: string) => {
    console.log(`Updating ${field} to:`, value)
    setOptimizationRequest(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Helper function to validate if service exists in architecture
  const validateServiceInArchitecture = (serviceName: string) => {
    const exists = availableServices.includes(serviceName)
    console.log(`Service "${serviceName}" exists in architecture:`, exists)
    return exists
  }

  const handleOptimize = async () => {
    try {
      setStep('processing')
      setProgress(0)

      // Debug: Log the optimization request
      console.log('Optimization request being sent:', optimizationRequest)
      console.log('Target service:', optimizationRequest.target_service)
      console.log('Available services in architecture:', availableServices)
      console.log('Architecture nodes:', nodes)

      // Validate that the target service exists in architecture
      if (!validateServiceInArchitecture(optimizationRequest.target_service)) {
        toast({
          title: "Service Not Found",
          description: `Service "${optimizationRequest.target_service}" not found in current architecture. Available services: ${availableServices.join(', ')}`,
          variant: "destructive"
        })
        setStep('input')
        return
      }

      // Debug: Test node processing with backend
      try {
        console.log('Testing node processing with backend...')
        const debugResult = await optimizationApi.debugNodes(optimizationRequest)
        console.log('Backend debug result:', debugResult)

        if (debugResult.extracted_services.length === 0) {
          toast({
            title: "Backend Processing Issue",
            description: `Backend extracted 0 services from ${debugResult.nodes_received} nodes. Check console for details.`,
            variant: "destructive"
          })
          setStep('input')
          return
        }
      } catch (debugError) {
        console.error('Debug test failed:', debugError)
        toast({
          title: "Backend Connection Issue",
          description: "Cannot communicate with backend for debugging. Proceeding with optimization...",
          variant: "destructive"
        })
      }

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 500)

      const result = await optimizationApi.optimizeArchitecture(optimizationRequest)
      
      clearInterval(progressInterval)
      setProgress(100)
      
      setTimeout(() => {
        setOptimizationResult(result)
        setStep('results')
      }, 500)

    } catch (error: any) {
      console.error('Optimization error:', error)
      toast({
        title: "Optimization Failed",
        description: error.message || "An error occurred during optimization",
        variant: "destructive"
      })
      setStep('input')
    }
  }

  const handleApplyRecommendation = () => {
    if (optimizationResult && onOptimizationApplied) {
      onOptimizationApplied(optimizationResult.recommendation)
      toast({
        title: "Optimization Applied",
        description: "Architecture has been updated with the optimization recommendation",
      })
      onClose()
    }
  }

  const handleReset = () => {
    setStep('input')
    setProgress(0)
    setOptimizationResult(null)
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'keep_current':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'optimize_configuration':
        return <Zap className="h-5 w-5 text-blue-500" />
      case 'replace_service':
        return <TrendingUp className="h-5 w-5 text-orange-500" />
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />
    }
  }

  const getActionColor = (action: string) => {
    switch (action) {
      case 'keep_current':
        return 'bg-green-100 text-green-800'
      case 'optimize_configuration':
        return 'bg-blue-100 text-blue-800'
      case 'replace_service':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Optimize Architecture
          </DialogTitle>
        </DialogHeader>

        {step === 'input' && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="target_service">Target Service</Label>
                <Select
                  value={optimizationRequest.target_service}
                  onValueChange={(value) => handleInputChange('target_service', value)}
                  disabled={isLoadingServices}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={
                      isLoadingServices
                        ? "Loading services..."
                        : availableServices.length > 0
                          ? "Select service to optimize"
                          : "No services available"
                    } />
                  </SelectTrigger>
                  <SelectContent className="max-h-[300px]">
                    {availableServices.length > 0 ? (
                      // Show services from current architecture
                      availableServices.map((service) => (
                        <SelectItem key={service} value={service}>
                          <div className="flex items-center space-x-2">
                            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                            <span>{service}</span>
                          </div>
                        </SelectItem>
                      ))
                    ) : (
                      <div className="px-2 py-4 text-center text-gray-500">
                        {isLoadingServices
                          ? "Loading services..."
                          : "No services found in current architecture. Please add services to your architecture first."
                        }
                      </div>
                    )}
                  </SelectContent>
                </Select>

                {availableServices.length > 0 ? (
                  <p className="text-xs text-blue-600">
                    ✓ {availableServices.length} services found in current architecture
                  </p>
                ) : (
                  <p className="text-xs text-orange-600">
                    ⚠ No services found. Add services to your architecture first.
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="unit">Time Unit</Label>
                <Select
                  value={optimizationRequest.unit}
                  onValueChange={(value) => handleInputChange('unit', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="second">Per Second</SelectItem>
                    <SelectItem value="minute">Per Minute</SelectItem>
                    <SelectItem value="hour">Per Hour</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="throughput">Throughput</Label>
                <Input
                  id="throughput"
                  type="number"
                  value={optimizationRequest.throughput}
                  onChange={(e) => handleInputChange('throughput', e.target.value)}
                  placeholder="1000"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="latency_requirement">Max Latency (seconds)</Label>
                <Input
                  id="latency_requirement"
                  type="number"
                  step="0.1"
                  value={optimizationRequest.latency_requirement}
                  onChange={(e) => handleInputChange('latency_requirement', e.target.value)}
                  placeholder="10"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="memory_requirement">Required Memory (MB)</Label>
                <Input
                  id="memory_requirement"
                  type="number"
                  value={optimizationRequest.memory_requirement}
                  onChange={(e) => handleInputChange('memory_requirement', e.target.value)}
                  placeholder="1024"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="current_memory">Current Memory (MB)</Label>
                <Input
                  id="current_memory"
                  type="number"
                  value={optimizationRequest.current_memory}
                  onChange={(e) => handleInputChange('current_memory', e.target.value)}
                  placeholder="1024"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="current_cores">Current CPU Cores</Label>
                <Input
                  id="current_cores"
                  type="number"
                  value={optimizationRequest.current_cores}
                  onChange={(e) => handleInputChange('current_cores', e.target.value)}
                  placeholder="2"
                />
              </div>
            </div>
          </div>
        )}

        {step === 'processing' && (
          <div className="space-y-6 py-8">
            <div className="text-center">
              <Zap className="h-12 w-12 mx-auto mb-4 text-blue-500 animate-pulse" />
              <h3 className="text-lg font-semibold mb-2">Optimizing Architecture</h3>
              <p className="text-gray-600 mb-4">
                Analyzing {optimizationRequest.target_service} and finding optimal configurations...
              </p>
              <Progress value={progress} className="w-full max-w-md mx-auto" />
              <p className="text-sm text-gray-500 mt-2">{progress}% complete</p>
            </div>
          </div>
        )}

        {step === 'results' && optimizationResult && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getActionIcon(optimizationResult.recommendation.action)}
                  Optimization Recommendation
                </CardTitle>
                <CardDescription>
                  Analysis of {optimizationResult.target_service} with {optimizationResult.alternatives_analyzed} alternatives
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge className={getActionColor(optimizationResult.recommendation.action)}>
                    {optimizationResult.recommendation.action.replace('_', ' ').toUpperCase()}
                  </Badge>
                  <span className="text-sm text-gray-600">
                    {optimizationResult.recommendation.change_type.replace('_', ' ')}
                  </span>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {optimizationResult.recommendation.recommendation_text}
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Current Service</Label>
                    <p className="text-sm">{optimizationResult.recommendation.current_service}</p>
                    <p className="text-xs text-gray-500">
                      Memory: {optimizationResult.current_configuration.memory} MB, 
                      Cores: {optimizationResult.current_configuration.cores}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Recommended Service</Label>
                    <p className="text-sm">{optimizationResult.recommendation.recommended_service}</p>
                    <p className="text-xs text-gray-500">
                      {optimizationResult.recommendation.justification}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <DialogFooter>
          {step === 'input' && (
            <>
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                onClick={handleOptimize}
                disabled={!optimizationRequest.target_service}
              >
                Start Optimization
              </Button>
            </>
          )}

          {step === 'processing' && (
            <Button variant="outline" onClick={() => setStep('input')}>
              Cancel
            </Button>
          )}

          {step === 'results' && (
            <>
              <Button variant="outline" onClick={handleReset}>
                Run Another Optimization
              </Button>
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              {optimizationResult?.recommendation.action !== 'keep_current' && (
                <Button onClick={handleApplyRecommendation}>
                  Apply Recommendation
                </Button>
              )}
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

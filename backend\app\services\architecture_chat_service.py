"""
Architecture Chat Service using LangChain

This service provides AI-powered chat functionality for architecture analysis,
optimization suggestions, and general questions about cloud architectures.
Includes web search capabilities and extensible tool framework.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.memory import ConversationBufferWindowMemory
from langchain_core.tools import Tool, StructuredTool
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from pydantic import BaseModel, Field
from langchain_openai import AzureChatOpenAI
from langchain_community.tools import DuckDuckGoSearchRun
from langchain_community.utilities import DuckDuckGoSearchAPIWrapper

from app.core.config import settings
from app.services.ml_service import ml_service
from app.services.optimization_service import OptimizationService

logger = logging.getLogger(__name__)

class ArchitectureChatService:
    """
    AI-powered chat service for architecture analysis and optimization
    """
    
    def __init__(self):
        """Initialize the architecture chat service"""
        self.llm = None
        self.memory = ConversationBufferWindowMemory(
            memory_key="chat_history",
            return_messages=True,
            k=10  # Keep last 10 exchanges
        )
        self.tools = []
        self.agent = None
        self.agent_executor = None
        self.optimization_service = OptimizationService()
        
        # Initialize Azure OpenAI LLM
        self._initialize_llm()
        
        # Initialize tools
        self._initialize_tools()
        
        # Initialize agent
        self._initialize_agent()
    
    def _initialize_llm(self):
        """Initialize Azure OpenAI LLM using existing configuration"""
        try:
            if settings.AZURE_OPENAI_API_KEY and settings.AZURE_OPENAI_ENDPOINT:
                self.llm = AzureChatOpenAI(
                    azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
                    api_key=settings.AZURE_OPENAI_API_KEY,
                    api_version=settings.AZURE_OPENAI_API_VERSION,
                    deployment_name=settings.AZURE_OPENAI_DEPLOYMENT_NAME,
                    temperature=0.7,
                    max_tokens=1000,
                    timeout=settings.AZURE_OPENAI_TIMEOUT_SECONDS
                )
                logger.info("Azure OpenAI LLM initialized successfully")
            else:
                logger.error("Azure OpenAI credentials not configured")
                raise ValueError("Azure OpenAI credentials missing")
        except Exception as e:
            logger.error(f"Failed to initialize Azure OpenAI LLM: {e}")
            raise
    
    def _initialize_tools(self):
        """Initialize available tools for the agent"""

        # Define input schemas for structured tools
        class ArchitectureAnalysisInput(BaseModel):
            architecture_data: str = Field(description="JSON string containing architecture nodes and edges")

        class CostCalculationInput(BaseModel):
            architecture_data: str = Field(description="JSON string containing architecture nodes and edges")

        class OptimizationInput(BaseModel):
            input_str: str = Field(description="Input format: 'service_name|architecture_json'")

        class WebSearchInput(BaseModel):
            query: str = Field(description="Search query for web search")

        # Web Search Tool
        search_wrapper = DuckDuckGoSearchAPIWrapper(
            max_results=5,
            time="d",  # Last day
            region="wt-wt",  # Worldwide
            safesearch="moderate"
        )
        web_search_tool = DuckDuckGoSearchRun(api_wrapper=search_wrapper)
        
        # Architecture Analysis Tool
        def analyze_architecture(architecture_data: str) -> str:
            """Analyze architecture and provide insights"""
            try:
                arch_data = json.loads(architecture_data)
                nodes = arch_data.get('nodes', [])
                edges = arch_data.get('edges', [])

                # Extract service information
                services = []
                for node in nodes:
                    if node.get('data', {}).get('service', {}).get('name'):
                        services.append(node['data']['service']['name'])

                analysis = f"""
Architecture Analysis:
- Total Services: {len(services)}
- Services Used: {', '.join(services)}
- Connections: {len(edges)}
- Architecture Type: {'Microservices' if len(services) > 3 else 'Monolithic'}

Key Insights:
- Service Distribution: {self._analyze_service_distribution(services)}
- Potential Optimizations: {self._suggest_optimizations(services)}
"""
                return analysis
            except Exception as e:
                return f"Error analyzing architecture: {str(e)}"
        
        # Cost Calculation Tool
        def calculate_architecture_cost(architecture_data: str) -> str:
            """Calculate total cost for the architecture"""
            try:
                arch_data = json.loads(architecture_data)
                nodes = arch_data.get('nodes', [])
                edges = arch_data.get('edges', [])

                # Use existing ML service for cost calculation
                result = ml_service.calculate_architecture_cost(
                    architecture_type="Microservices",
                    nodes=nodes,
                    edges=edges,
                    architecture_data=arch_data
                )

                if result.get('status') == 'success':
                    total_cost = result.get('total_cost', 0)
                    breakdown = result.get('service_breakdown', [])

                    cost_summary = f"Total Architecture Cost: ${total_cost:.2f}/hour\n\nService Breakdown:\n"
                    for service in breakdown:
                        cost_summary += f"- {service.get('service_name', 'Unknown')}: ${service.get('cost', 0):.2f}/hour\n"

                    return cost_summary
                else:
                    return f"Error calculating cost: {result.get('error', 'Unknown error')}"
            except Exception as e:
                return f"Error calculating architecture cost: {str(e)}"
        
        # Optimization Suggestions Tool (sync wrapper for async function)
        def suggest_optimizations(service_name: str, architecture_data: str) -> str:
            """Suggest optimizations for a specific service"""
            try:
                arch_data = json.loads(architecture_data)

                optimization_params = {
                    'target_service': service_name,
                    'throughput': '1000',
                    'latency_requirement': '5.0',
                    'memory_requirement': '1024',
                    'current_memory': '1024',
                    'current_cores': '2',
                    'unit': 'second',
                    'nodes': arch_data.get('nodes', []),
                    'edges': arch_data.get('edges', [])
                }

                # Use asyncio.run to handle the async call
                result = asyncio.run(self.optimization_service.optimize_architecture(optimization_params))

                if result.get('status') == 'success':
                    recommendation = result.get('recommendation', {})
                    return f"""
Optimization Recommendation for {service_name}:
Action: {recommendation.get('action', 'No action needed')}
Recommended Service: {recommendation.get('recommended_service', 'Current service')}
Justification: {recommendation.get('justification', 'No specific recommendations')}
"""
                else:
                    return f"Error getting optimization suggestions: {result.get('error', 'Unknown error')}"
            except Exception as e:
                return f"Error suggesting optimizations: {str(e)}"
        
        # Define tools list using StructuredTool for better argument handling
        self.tools = [
            StructuredTool.from_function(
                name="web_search",
                description="Search the web for current information about cloud services, best practices, or technical questions. Use this when you need up-to-date information.",
                func=lambda query: web_search_tool.run(query),
                args_schema=WebSearchInput
            ),
            StructuredTool.from_function(
                name="analyze_architecture",
                description="Analyze a cloud architecture and provide insights. Input should be JSON string containing nodes and edges.",
                func=analyze_architecture,
                args_schema=ArchitectureAnalysisInput
            ),
            StructuredTool.from_function(
                name="calculate_cost",
                description="Calculate the total cost of a cloud architecture. Input should be JSON string containing nodes and edges.",
                func=calculate_architecture_cost,
                args_schema=CostCalculationInput
            ),
            StructuredTool.from_function(
                name="suggest_optimizations",
                description="Suggest optimizations for a specific service in the architecture. Input format: 'service_name|architecture_json'",
                func=self._handle_optimization_tool,
                args_schema=OptimizationInput
            )
        ]
        
        logger.info(f"Initialized {len(self.tools)} tools for architecture chat")
    
    def _handle_optimization_tool(self, input_str: str) -> str:
        """Handle optimization tool input parsing"""
        try:
            parts = input_str.split('|', 1)
            if len(parts) != 2:
                return "Invalid input format. Use: 'service_name|architecture_json'"

            service_name, architecture_data = parts

            # Call the optimization function directly (it's now sync)
            arch_data = json.loads(architecture_data.strip())

            optimization_params = {
                'target_service': service_name.strip(),
                'throughput': '1000',
                'latency_requirement': '5.0',
                'memory_requirement': '1024',
                'current_memory': '1024',
                'current_cores': '2',
                'unit': 'second',
                'nodes': arch_data.get('nodes', []),
                'edges': arch_data.get('edges', [])
            }

            # Use asyncio.run to handle the async call
            result = asyncio.run(self.optimization_service.optimize_architecture(optimization_params))

            if result.get('status') == 'success':
                recommendation = result.get('recommendation', {})
                return f"""
Optimization Recommendation for {service_name.strip()}:
Action: {recommendation.get('action', 'No action needed')}
Recommended Service: {recommendation.get('recommended_service', 'Current service')}
Justification: {recommendation.get('justification', 'No specific recommendations')}
"""
            else:
                return f"Error getting optimization suggestions: {result.get('error', 'Unknown error')}"
        except Exception as e:
            return f"Error processing optimization request: {str(e)}"
    
    def _initialize_agent(self):
        """Initialize the LangChain agent with tools"""
        try:
            # Create prompt template
            prompt = ChatPromptTemplate.from_messages([
                ("system", """You are an expert AWS cloud architect assistant. You help users analyze, optimize, and understand cloud architectures.

You have access to the following tools:
1. web_search: Search for current cloud information and best practices. Input: query string
2. analyze_architecture: Analyze architecture and provide insights. Input: architecture_data (JSON string)
3. calculate_cost: Calculate total architecture cost. Input: architecture_data (JSON string)
4. suggest_optimizations: Get optimization suggestions. Input: input_str (format: 'service_name|architecture_json')

IMPORTANT: When using tools, only pass the required input parameter. Do not pass additional configuration or metadata.

For architecture analysis and cost calculation, extract the architecture data from the conversation context and pass it as a JSON string to the appropriate tool.

Always be specific and provide actionable insights based on the tool results."""),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad")
            ])
            
            # Create agent
            self.agent = create_openai_tools_agent(
                llm=self.llm,
                tools=self.tools,
                prompt=prompt
            )
            
            # Create agent executor
            self.agent_executor = AgentExecutor(
                agent=self.agent,
                tools=self.tools,
                memory=self.memory,
                verbose=True,
                max_iterations=3,
                early_stopping_method="generate"
            )
            
            logger.info("Architecture chat agent initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize agent: {e}")
            raise
    
    def _analyze_service_distribution(self, services: List[str]) -> str:
        """Analyze the distribution of services in the architecture"""
        if not services:
            return "No services found"
        
        service_categories = {
            'Compute': ['EC2', 'Lambda', 'Fargate', 'ECS', 'EKS'],
            'Storage': ['S3', 'EFS', 'EBS', 'Glacier'],
            'Database': ['DynamoDB', 'RDS', 'ElastiCache', 'Redshift'],
            'Networking': ['API Gateway', 'CloudFront', 'Load Balancer', 'VPC'],
            'AI/ML': ['SageMaker', 'Rekognition', 'Comprehend']
        }
        
        distribution = {}
        for service in services:
            for category, category_services in service_categories.items():
                if any(cat_service.lower() in service.lower() for cat_service in category_services):
                    distribution[category] = distribution.get(category, 0) + 1
                    break
        
        if distribution:
            return ", ".join([f"{cat}: {count}" for cat, count in distribution.items()])
        else:
            return "Mixed/Other services"
    
    def _suggest_optimizations(self, services: List[str]) -> str:
        """Provide basic optimization suggestions based on services"""
        suggestions = []
        
        if any('EC2' in service for service in services):
            suggestions.append("Consider Lambda for event-driven workloads")
        
        if any('S3' in service for service in services):
            suggestions.append("Use S3 Intelligent Tiering for cost optimization")
        
        if len(services) > 5:
            suggestions.append("Consider service consolidation to reduce complexity")
        
        return "; ".join(suggestions) if suggestions else "Architecture looks well-optimized"

    async def chat_with_architecture(
        self,
        user_query: str,
        architecture_data: Optional[Dict[str, Any]] = None,
        conversation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Main chat interface for architecture-related queries

        Args:
            user_query: User's question or request
            architecture_data: Current architecture data (nodes, edges)
            conversation_id: Optional conversation identifier

        Returns:
            Dictionary containing AI response and metadata
        """
        try:
            # Prepare context with architecture data if provided
            context_info = ""
            if architecture_data:
                nodes = architecture_data.get('nodes', [])
                edges = architecture_data.get('edges', [])

                # Extract service names for context
                services = []
                for node in nodes:
                    service_name = node.get('data', {}).get('service', {}).get('name')
                    if service_name and 'User' not in service_name:
                        services.append(service_name)

                context_info = f"""
Current Architecture Context:
- Services: {', '.join(services) if services else 'None'}
- Total Components: {len(nodes)}
- Connections: {len(edges)}
- Architecture Data: {json.dumps(architecture_data)}
"""

            # Combine user query with context
            enhanced_query = f"{context_info}\n\nUser Question: {user_query}" if context_info else user_query

            # Execute agent with the query
            logger.info(f"Executing agent with query: {enhanced_query[:200]}...")
            response = await asyncio.to_thread(
                self.agent_executor.invoke,
                {"input": enhanced_query}
            )
            logger.info(f"Agent response received: {type(response)}")

            ai_response = response.get('output', 'I apologize, but I could not generate a response.')

            return {
                'status': 'success',
                'response': ai_response,
                'conversation_id': conversation_id or f"chat_{datetime.now().isoformat()}",
                'timestamp': datetime.now().isoformat(),
                'tools_used': self._extract_tools_used(response),
                'context_provided': bool(architecture_data)
            }

        except Exception as e:
            logger.error(f"Error in chat_with_architecture: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'response': 'I apologize, but I encountered an error while processing your request. Please try again.',
                'conversation_id': conversation_id,
                'timestamp': datetime.now().isoformat()
            }

    def _extract_tools_used(self, agent_response: Dict[str, Any]) -> List[str]:
        """Extract which tools were used in the agent response"""
        tools_used = []

        # Check intermediate steps for tool usage
        intermediate_steps = agent_response.get('intermediate_steps', [])
        for step in intermediate_steps:
            if len(step) >= 2 and hasattr(step[0], 'tool'):
                tools_used.append(step[0].tool)

        return tools_used

    async def get_architecture_insights(self, architecture_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get comprehensive insights about an architecture

        Args:
            architecture_data: Architecture data containing nodes and edges

        Returns:
            Dictionary containing various insights and analysis
        """
        try:
            insights_query = f"""
Please provide a comprehensive analysis of this cloud architecture:
{json.dumps(architecture_data)}

Include:
1. Architecture overview and pattern identification
2. Cost analysis and optimization opportunities
3. Security considerations
4. Scalability assessment
5. Best practices recommendations
"""

            result = await self.chat_with_architecture(
                user_query=insights_query,
                architecture_data=architecture_data
            )

            return result

        except Exception as e:
            logger.error(f"Error getting architecture insights: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

    async def suggest_architecture_improvements(
        self,
        architecture_data: Dict[str, Any],
        focus_area: str = "general"
    ) -> Dict[str, Any]:
        """
        Suggest specific improvements for the architecture

        Args:
            architecture_data: Current architecture data
            focus_area: Area to focus on (cost, performance, security, etc.)

        Returns:
            Dictionary containing improvement suggestions
        """
        try:
            improvement_query = f"""
Based on this architecture, suggest specific improvements focusing on {focus_area}:
{json.dumps(architecture_data)}

Please provide:
1. Specific actionable recommendations
2. Expected benefits of each recommendation
3. Implementation complexity (Low/Medium/High)
4. Cost impact (if applicable)
5. Priority ranking of suggestions
"""

            result = await self.chat_with_architecture(
                user_query=improvement_query,
                architecture_data=architecture_data
            )

            return result

        except Exception as e:
            logger.error(f"Error suggesting improvements: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

    def add_custom_tool(self, tool: Tool) -> bool:
        """
        Add a custom tool to the agent

        Args:
            tool: LangChain Tool object

        Returns:
            Boolean indicating success
        """
        try:
            self.tools.append(tool)
            # Reinitialize agent with new tools
            self._initialize_agent()
            logger.info(f"Added custom tool: {tool.name}")
            return True
        except Exception as e:
            logger.error(f"Error adding custom tool: {e}")
            return False

    def get_conversation_history(self, conversation_id: str) -> List[Dict[str, Any]]:
        """
        Get conversation history for a specific conversation

        Args:
            conversation_id: Conversation identifier

        Returns:
            List of conversation messages
        """
        try:
            messages = self.memory.chat_memory.messages
            history = []

            for message in messages:
                if isinstance(message, HumanMessage):
                    history.append({
                        'role': 'user',
                        'content': message.content,
                        'timestamp': datetime.now().isoformat()
                    })
                elif isinstance(message, AIMessage):
                    history.append({
                        'role': 'assistant',
                        'content': message.content,
                        'timestamp': datetime.now().isoformat()
                    })

            return history
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []

    def clear_conversation_history(self) -> bool:
        """Clear the conversation history"""
        try:
            self.memory.clear()
            logger.info("Conversation history cleared")
            return True
        except Exception as e:
            logger.error(f"Error clearing conversation history: {e}")
            return False

# Global instance
architecture_chat_service = ArchitectureChatService()

"""
API router for version 1
"""

from fastapi import APIRouter

from app.api.v1.endpoints import ml_predictions, rag, chat, requirements, vpc, auth, admin, image_processing, optimization, architecture_chat
from app.api import architecture


api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    ml_predictions.router,
    prefix="/ml",
    tags=["Machine Learning Predictions"]
)

api_router.include_router(
    rag.router,
    prefix="/rag",
    tags=["RAG (Retrieval Augmented Generation)"]
)

api_router.include_router(
    chat.router,
    prefix="/chat",
    tags=["AI Chat"]
)

api_router.include_router(
    requirements.router,
    prefix="/requirements",
    tags=["Requirements Analysis"]
)

api_router.include_router(
    architecture.router,
    prefix="/architecture",
    tags=["Architecture Management"]
)

api_router.include_router(
    vpc.router,
    prefix="/vpc",
    tags=["VPC Management"]
)

api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"]
)

api_router.include_router(
    admin.router,
    prefix="/admin",
    tags=["Admin"]
)

api_router.include_router(
    image_processing.router,
    prefix="/image-processing",
    tags=["Image Processing"]
)

api_router.include_router(
    optimization.router,
    prefix="/optimization",
    tags=["Architecture Optimization"]
)

api_router.include_router(
    architecture_chat.router,
    prefix="/architecture-chat",
    tags=["Architecture AI Chat"]
)

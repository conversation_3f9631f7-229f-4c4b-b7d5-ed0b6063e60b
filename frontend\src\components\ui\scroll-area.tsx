/**
 * ScrollArea Component
 * 
 * A custom scroll area component that uses the project's custom scrollbar styles
 * defined in index.css. This provides a consistent scrolling experience across
 * the application.
 */

import * as React from "react"
import { cn } from "@/lib/utils"

interface ScrollAreaProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
  orientation?: "vertical" | "horizontal" | "both"
  type?: "auto" | "always" | "scroll" | "hover"
}

const ScrollArea = React.forwardRef<HTMLDivElement, ScrollAreaProps>(
  ({ className, children, orientation = "vertical", type = "auto", ...props }, ref) => {
    const getScrollClasses = () => {
      const baseClasses = "custom-scrollbar"
      
      switch (orientation) {
        case "horizontal":
          return `${baseClasses} overflow-x-auto overflow-y-hidden`
        case "both":
          return `${baseClasses} overflow-auto`
        case "vertical":
        default:
          return `${baseClasses} overflow-y-auto overflow-x-hidden`
      }
    }

    const getScrollBehavior = () => {
      switch (type) {
        case "always":
          return "scrollbar-visible"
        case "scroll":
          return "overflow-scroll"
        case "hover":
          return "hover:scrollbar-visible"
        case "auto":
        default:
          return ""
      }
    }

    return (
      <div
        ref={ref}
        className={cn(
          getScrollClasses(),
          getScrollBehavior(),
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

ScrollArea.displayName = "ScrollArea"

// Additional scroll area components for more specific use cases
const ScrollAreaViewport = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("h-full w-full", className)}
    {...props}
  >
    {children}
  </div>
))

ScrollAreaViewport.displayName = "ScrollAreaViewport"

const ScrollAreaScrollbar = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    orientation?: "vertical" | "horizontal"
  }
>(({ className, orientation = "vertical", ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex touch-none select-none transition-colors",
      orientation === "vertical" &&
        "h-full w-2.5 border-l border-l-transparent p-[1px]",
      orientation === "horizontal" &&
        "h-2.5 w-full border-t border-t-transparent p-[1px]",
      className
    )}
    {...props}
  />
))

ScrollAreaScrollbar.displayName = "ScrollAreaScrollbar"

const ScrollAreaThumb = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "relative flex-1 rounded-full bg-border",
      className
    )}
    {...props}
  />
))

ScrollAreaThumb.displayName = "ScrollAreaThumb"

const ScrollAreaCorner = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "bg-blackA6 h-2.5 w-2.5",
      className
    )}
    {...props}
  />
))

ScrollAreaCorner.displayName = "ScrollAreaCorner"

export {
  ScrollArea,
  ScrollAreaViewport,
  ScrollAreaScrollbar,
  ScrollAreaThumb,
  ScrollAreaCorner,
}

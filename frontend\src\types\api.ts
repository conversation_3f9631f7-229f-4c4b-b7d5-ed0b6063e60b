// API Types for NOAH Arch Frontend

export interface ApiResponse<T = any> {
  status: 'success' | 'error'
  message?: string
  data?: T
  error?: string
}

// Authentication Types
export interface User {
  id: number
  username: string
  email?: string
  full_name?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  last_login?: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  password: string
  email?: string
  full_name?: string
}

export interface TokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
}

export interface RefreshTokenRequest {
  refresh_token: string
}

export interface UpdateProfileRequest {
  email?: string
  full_name?: string
  password?: string
}

// Admin Types
export interface AdminUser extends User {
  architecture_count: number
  total_cost_calculations: number
  last_activity?: string
  session_count: number
}

export interface SystemStats {
  total_users: number
  active_users: number
  inactive_users: number
  superusers: number
  total_architectures: number
  total_cost_calculations: number
  total_requirement_files: number
  ml_cache_entries: number
  new_users_last_7_days: number
  new_architectures_last_7_days: number
  active_sessions: number
  database_size_mb: number
  uptime_hours: number
}

export interface UserActivity {
  user_id: number
  username: string
  activity_type: string
  activity_description: string
  timestamp: string
  ip_address?: string
}

// ML Prediction Types
export interface LambdaCostRequest {
  workload_invocations: number
  memory_mb?: number
  function_purpose?: string
  memory_required?: number
}

export interface LambdaCostResponse {
  latency: number
  cost: number
  max_latency: number
  total_execution_cost: number
  total_invocation_cost: number
  memory_gb: number
  details?: any
}

export interface S3CostRequest {
  workload: number
  file_size?: number
  memory?: number
  operation?: 'read' | 'write'
}

export interface S3CostResponse {
  latency: number
  cost: number
  predicted_latencies: number[]
  operation_type: string
  details?: any
}

export interface DynamoDBCostRequest {
  workload: number
  data_size?: number
  mem_config?: number
  chunk_size?: number
  num_tables?: number
  num_threads?: number
}

export interface DynamoDBCostResponse {
  latency: number
  cost: number
  write_cost: number
  storage_cost: number
  total_item_size_mb: number
  details?: any
}

export interface APIGatewayCostRequest {
  requests_per_hour: number
  input_payload_size_kb: number
  output_payload_size_kb: number
}

export interface APIGatewayCostResponse {
  latency: number
  cost: number
  requests_per_hour: number
  input_payload_size_kb: number
  output_payload_size_kb: number
  details?: any
}

export interface EC2CostRequest {
  instanceType: string
  LLMModel: string
  batchSize: number
  inputTokens: number
  outputTokens: number
}

export interface EC2CostResponse {
  latency: number
  cost: number
  instanceType: string
  LLMModel: string
  batchSize: number
  inputTokens: number
  outputTokens: number
  details?: any
}

// Architecture Cost Calculation Types
export interface ArchitectureCostRequest {
  architecture_type: 'Monolith' | 'Microservices'
  nodes: Array<{
    id: string
    service_id: string
    service_name: string
    config: Record<string, any>
    position?: { x: number; y: number }
  }>
  edges: Array<{
    id: string
    source: string
    target: string
    config?: Record<string, any>
  }>
  architecture: {
    name: string
    description?: string
    uses_vpc?: boolean
    nat_gateways?: number
  }
}

export interface ArchitectureCostResponse {
  total_cost: number
  total_latency: number
  cost_per_request: number
  cost_per_1000_requests: number
  architecture_type: string
  service_breakdown: Array<{
    service_id: string
    service_name: string
    cost: number
    latency: number
    percentage?: number
    details?: any
  }>
  network_costs: number
  vpc_costs: number
  recommendations?: string[]
  details?: any
}

// VPC Types
export interface VPCConfigRequest {
  connections: number
  hours_per_day: number
  nat_gateways: number
  nat_data_gb_per_month: number
  inuse_public_ipv4: number
  idle_public_ipv4: number
}

export interface VPCCreateRequest {
  service_ids: string[]
  config: VPCConfigRequest
  name?: string
}

export interface VPCUpdateRequest {
  config?: VPCConfigRequest
  service_ids?: string[]
  name?: string
}

export interface VPCServiceUpdateRequest {
  service_ids: string[]
}

export interface VPCConfigResponse {
  connections: number
  hours_per_day: number
  nat_gateways: number
  nat_data_gb_per_month: number
  inuse_public_ipv4: number
  idle_public_ipv4: number
  cost: number
}

export interface VPCBoundaryResponse {
  top_left_x: number
  top_left_y: number
  width: number
  height: number
  color: string
  border_thickness: number
}

export interface VPCResponse {
  vpc_id: number
  name?: string
  service_ids: string[]
  config: VPCConfigResponse
  boundary?: VPCBoundaryResponse
  color: string
  created_at: string
  updated_at: string
}

export interface VPCListResponse {
  vpcs: VPCResponse[]
  total_count: number
  total_vpc_cost: number
}

export interface VPCCostResponse {
  vpc_id: number
  cost: number
  cost_breakdown: Record<string, number>
  config: VPCConfigResponse
  calculation_details: Record<string, any>
}

// RAG Types
export interface RAGQueryRequest {
  query: string
  top_k?: number
  include_context?: boolean
}

export interface RAGQueryResponse {
  answer: string
  context?: string[]
  confidence?: number
  sources?: string[]
}

export interface DocumentUploadResponse {
  document_id: string
  filename: string
  chunks_created: number
  embeddings_created: number
  status: string
  message: string
}

export interface RAGArchitectureRequest {
  num_users: string
  num_documents: string
  doc_type: string
  query_type: string
}

export interface RAGArchitectureResponse {
  embedding_model: string
  vector_db: string
  llm: string
  latency: number
  cost: number
  composite_score?: number
}

// Chat Types
export interface ChatMessageRequest {
  message: string
  conversation_id?: string
  include_history?: boolean
  max_tokens?: number
}

export interface ChatMessageResponse {
  response: string
  conversation_id: string
  timestamp: string
  tokens_used?: number
}

// Requirements Types
export interface RequirementAnalysisRequest {
  file_content: string
  filename: string
}

export interface DynamicQuestion {
  question_id: string
  question: string
  category: string
  question_type: 'open' | 'mcq' | 'yes_no'
  options?: string[]
  validation_hint: string
  follow_up: string
  clarification: string
  required: boolean
}

export interface RequirementAnalysisResponse {
  status: string
  filename: string
  content_summary: string
  missing_categories: string[]
  dynamic_questions: DynamicQuestion[]
  message: string
}

export interface DynamicQuestionResponse {
  question_id: string
  question: string
  answer: string
  category: string
}

export interface ArchitectureGenerationRequest {
  requirements: string
  answers: DynamicQuestionResponse[]
  include_cost_analysis?: boolean
}

export interface ArchitectureGenerationResponse {
  status: string
  architecture: Record<string, any>
  services: string[]
  connections: string[]
  cost_analysis?: Record<string, any>
  message: string
}

// Architecture Types
export interface ArchitectureComponent {
  id: string
  type: string
  name: string
  position: { x: number; y: number }
  properties: Record<string, any>
}

export interface ArchitectureConnection {
  id: string
  source: string
  target: string
  type?: string
}

export interface Architecture {
  id?: string
  name: string
  description?: string
  components: ArchitectureComponent[]
  connections: ArchitectureConnection[]
  metadata?: Record<string, any>
}

import { Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from '@/components/ui/toaster'
import { AuthProvider } from '@/contexts/AuthContext'
import { ThemeProvider } from '@/contexts/ThemeContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import MainLayout from '@/components/layout/MainLayout'

// Pages
import Login from '@/pages/Login'
import Register from '@/pages/Register'
import Dashboard from '@/pages/Dashboard'
import ArchitectureDesigner from '@/pages/ArchitectureDesigner'
import EnhancedArchitectureDesigner from '@/pages/EnhancedArchitectureDesigner'
import Requirements from '@/pages/Requirements'
import Chat from '@/pages/Chat'
import CostAnalysis from '@/pages/CostAnalysis'
import Documentation from '@/pages/Documentation'
import AdminPortal from '@/pages/AdminPortal'

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="noaharch-theme">
      <AuthProvider>
        <div className="min-h-screen bg-background font-sans antialiased">
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            
            {/* Protected routes */}
            <Route
              path="/*"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <Routes>
                      <Route path="/" element={<Dashboard />} />
                      <Route path="/architecture" element={<ArchitectureDesigner />} />
                      <Route path="/architecture-enhanced" element={<EnhancedArchitectureDesigner />} />
                      <Route path="/requirements" element={<Requirements />} />
                      <Route path="/chat" element={<Chat />} />
                      <Route path="/cost-analysis" element={<CostAnalysis />} />
                      <Route path="/documentation" element={<Documentation />} />
                      <Route path="/admin" element={<AdminPortal />} />
                      
                      {/* Catch all route */}
                      <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>
                  </MainLayout>
                </ProtectedRoute>
              }
            />
          </Routes>
          
          {/* Global toast notifications */}
          <Toaster />
        </div>
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App

import { useState, useCallback, useMemo, useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu'
import { OnNodesChange, OnEdgesChange, OnConnect, addEdge, applyNodeChanges, applyEdgeChanges } from 'reactflow'
import {
  Network,
  Plus,
  Save,
  Upload,
  Download,
  Trash2,
  FileText,
  FileImage,
  Zap,
  ChevronDown
} from 'lucide-react'

// Architecture components
import ArchitectureCanvas from '@/components/architecture/ArchitectureCanvas'
import ComponentPalette from '@/components/architecture/ComponentPalette'
import PropertiesPanel from '@/components/architecture/PropertiesPanel'
import CostCalculationPanel from '@/components/architecture/CostCalculationPanel'
import TemplateSelector from '@/components/architecture/TemplateSelector'
import SelectiveCostCalculation from '@/components/architecture/SelectiveCostCalculation'
import VPCManagement from '@/components/architecture/VPCManagement'
import LoadImageDialog from '@/components/architecture/LoadImageDialog'
import OptimizationDialog from '@/components/optimization/OptimizationDialog'
import { CloudService, ArchitectureNode, ArchitectureEdge, Architecture, CostAnalysis } from '@/types/architecture'
import { getServiceById } from '@/components/architecture/utils/serviceDefinitions'
import { imageProcessingService } from '@/services/imageProcessingService'
import { mlApi, vpcApi, architectureApi } from '@/services/api'
import { convertMapleGUIToReactFlow } from '@/utils/architectureConverter'
import { optimizeArchitectureLayout } from '@/utils/layoutUtils'

export default function ArchitectureDesigner() {
  const [isDesignMode, setIsDesignMode] = useState(false)
  const [nodes, setNodes] = useState<ArchitectureNode[]>([])
  const [edges, setEdges] = useState<ArchitectureEdge[]>([])
  const [selectedNode, setSelectedNode] = useState<ArchitectureNode | null>(null)
  const [architectureName, setArchitectureName] = useState('Untitled Architecture')
  const [costAnalysis, setCostAnalysis] = useState<CostAnalysis | null>(null)
  const [activeTab, setActiveTab] = useState<'properties' | 'cost' | 'selective' | 'vpc'>('properties')
  const [showTemplateSelector, setShowTemplateSelector] = useState(false)
  const [showLoadImageDialog, setShowLoadImageDialog] = useState(false)
  const [showOptimizationDialog, setShowOptimizationDialog] = useState(false)
  const [optimizationTargetService, setOptimizationTargetService] = useState<string>('')
  const [useFileSystemAPI, setUseFileSystemAPI] = useState(true) // Preference for "Save As" dialog
  const [triggerFitView, setTriggerFitView] = useState(false)
  const { toast } = useToast()
  const location = useLocation()

  // Load generated architecture from requirements if available
  useEffect(() => {
    const loadGeneratedArchitecture = async () => {
      console.log('ArchitectureDesigner useEffect triggered')
      console.log('Location state:', location.state)

      // Check if we came from requirements page with generated architecture
      if (location.state?.fromRequirements && location.state?.architectureData) {
        const architectureData = location.state.architectureData

        try {
          console.log('Loading generated architecture from requirements:', architectureData)
          console.log('Nodes to load:', architectureData.nodes)
          console.log('Edges to load:', architectureData.edges)

          // Validate and convert nodes to proper ArchitectureNode format
          const validatedNodes = (architectureData.nodes || []).map((node: any) => ({
            ...node,
            // Ensure all required properties are present
            selected: node.selected || false,
            dragging: node.dragging || false,
            width: node.width || 200,
            height: node.height || 100,
            zIndex: node.zIndex || 0,
            hidden: node.hidden || false,
            deletable: node.deletable !== false,
            selectable: node.selectable !== false,
            connectable: node.connectable !== false,
            focusable: node.focusable !== false
          }))

          // Validate and convert edges to proper ArchitectureEdge format
          const validatedEdges = (architectureData.edges || []).map((edge: any) => ({
            ...edge,
            // Ensure all required properties are present
            animated: edge.animated || false,
            style: edge.style || { stroke: '#666', strokeWidth: 2 },
            sourceHandle: edge.sourceHandle || null,
            targetHandle: edge.targetHandle || null,
            selected: edge.selected || false,
            hidden: edge.hidden || false,
            deletable: edge.deletable !== false,
            selectable: edge.selectable !== false,
            focusable: edge.focusable !== false
          }))

          console.log('Validated nodes:', validatedNodes)
          console.log('Validated edges:', validatedEdges)

          // ALWAYS apply layout optimization for requirement analysis results
          console.log('🎯 Applying mandatory layout optimization for requirement analysis results')
          const { nodes: optimizedNodes, edges: optimizedEdges } = optimizeArchitectureLayout(validatedNodes, validatedEdges)

          // Set the optimized architecture data
          setNodes(optimizedNodes)
          setEdges(optimizedEdges)
          setIsDesignMode(true)
          setArchitectureName('Generated Architecture')

          toast({
            title: "Architecture Loaded",
            description: `Generated architecture loaded with ${validatedNodes.length} services and ${validatedEdges.length} connections.`,
          })
        } catch (error) {
          console.error('Error loading generated architecture:', error)
          toast({
            title: "Load Error",
            description: "Failed to load generated architecture.",
            variant: "destructive"
          })
        }
      }

      // Also check sessionStorage for generated architecture
      else {
        const storedArchitecture = sessionStorage.getItem('generatedArchitecture')
        if (storedArchitecture) {
          try {
            const architectureData = JSON.parse(storedArchitecture)
            console.log('Loading architecture from sessionStorage:', architectureData)

            // Apply same validation as above
            const validatedNodes = (architectureData.nodes || []).map((node: any) => ({
              ...node,
              selected: node.selected || false,
              dragging: node.dragging || false,
              width: node.width || 200,
              height: node.height || 100,
              zIndex: node.zIndex || 0,
              hidden: node.hidden || false,
              deletable: node.deletable !== false,
              selectable: node.selectable !== false,
              connectable: node.connectable !== false,
              focusable: node.focusable !== false
            }))

            const validatedEdges = (architectureData.edges || []).map((edge: any) => ({
              ...edge,
              animated: edge.animated || false,
              style: edge.style || { stroke: '#666', strokeWidth: 2 },
              sourceHandle: edge.sourceHandle || null,
              targetHandle: edge.targetHandle || null,
              selected: edge.selected || false,
              hidden: edge.hidden || false,
              deletable: edge.deletable !== false,
              selectable: edge.selectable !== false,
              focusable: edge.focusable !== false
            }))

            // Apply complete layout optimization to generated nodes and edges
            const { nodes: optimizedNodes, edges: optimizedEdges } = optimizeArchitectureLayout(validatedNodes, validatedEdges)

            setNodes(optimizedNodes)
            setEdges(optimizedEdges)
            setIsDesignMode(true)
            setArchitectureName('Generated Architecture')

            // Clear from sessionStorage after loading
            sessionStorage.removeItem('generatedArchitecture')

            toast({
              title: "Architecture Loaded",
              description: `Generated architecture loaded with ${validatedNodes.length} services and ${validatedEdges.length} connections.`,
            })
          } catch (error) {
            console.error('Error parsing stored architecture:', error)
            sessionStorage.removeItem('generatedArchitecture') // Clear invalid data
          }
        }
      }
    }

    loadGeneratedArchitecture()
  }, [location, toast])

  // Helper function to download file with better browser compatibility
  const downloadFile = (blob: Blob, filename: string, description: string) => {
    // For browsers that don't support File System Access API or when disabled
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename

    // Add some attributes that might help trigger "Save As" dialog
    link.setAttribute('target', '_blank')
    link.style.display = 'none'

    // Add to document, click, and remove
    document.body.appendChild(link)
    link.click()

    // Clean up after a short delay
    setTimeout(() => {
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }, 100)

    toast({
      title: "Architecture Saved",
      description: description,
    })
  }

  // React Flow event handlers
  const onNodesChange: OnNodesChange = useCallback(
    (changes) => {
      setNodes((nds) => {
        // Convert our ArchitectureNode[] to React Flow Node[] format
        const reactFlowNodes = nds.map(node => ({
          id: node.id,
          type: node.type,
          position: node.position,
          data: node.data,
          selected: node.selected,
          dragging: node.dragging
        }))

        // Apply React Flow changes
        const updatedReactFlowNodes = applyNodeChanges(changes, reactFlowNodes)

        // Convert back to our ArchitectureNode[] format
        return updatedReactFlowNodes.map(node => ({
          id: node.id,
          type: node.type,
          position: node.position,
          data: node.data,
          selected: node.selected,
          dragging: node.dragging,
          width: node.width,
          height: node.height,
          zIndex: node.zIndex,
          hidden: node.hidden,
          deletable: node.deletable !== false, // Default to true
          selectable: node.selectable !== false, // Default to true
          connectable: node.connectable !== false, // Default to true
          focusable: node.focusable !== false // Default to true
        }))
      })
    },
    []
  )

  const onEdgesChange: OnEdgesChange = useCallback(
    (changes) => {
      setEdges((eds) => {
        // Convert our ArchitectureEdge[] to React Flow Edge[] format
        const reactFlowEdges = eds.map(edge => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          type: edge.type || 'default',
          animated: edge.animated || false,
          style: edge.style || {}
        }))

        // Apply React Flow changes
        const updatedReactFlowEdges = applyEdgeChanges(changes, reactFlowEdges)

        // Convert back to our ArchitectureEdge[] format
        return updatedReactFlowEdges.map(edge => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          type: edge.type,
          animated: edge.animated,
          style: edge.style,
          sourceHandle: edge.sourceHandle,
          targetHandle: edge.targetHandle,
          selected: edge.selected,
          hidden: edge.hidden,
          deletable: edge.deletable !== false, // Default to true
          selectable: edge.selectable !== false, // Default to true
          focusable: edge.focusable !== false // Default to true
        }))
      })
    },
    []
  )

  const onConnect: OnConnect = useCallback(
    (connection) => {
      console.log('Creating connection:', connection)

      const edge: ArchitectureEdge = {
        id: `edge-${Date.now()}`,
        source: connection.source!,
        target: connection.target!,
        type: 'smoothstep',
        animated: false,
        style: {
          stroke: '#6366f1',
          strokeWidth: 2
        },
        markerEnd: 'arrowclosed',
        // Preserve handle information to maintain connection points
        sourceHandle: connection.sourceHandle || null,
        targetHandle: connection.targetHandle || null,
        data: {
          cost: 0,
          latency: 0,
          bandwidth: '',
          protocol: 'TCP'
        }
      }
      setEdges((eds) => addEdge(edge, eds))

      // Log the connection details for debugging
      const sourceHandle = connection.sourceHandle || 'default'
      const targetHandle = connection.targetHandle || 'default'
      console.log(`Connection created: ${connection.source}[${sourceHandle}] -> ${connection.target}[${targetHandle}]`)

      toast({
        title: "Connection Created",
        description: `Components connected via ${sourceHandle} → ${targetHandle} handles.`,
      })
    },
    [toast]
  )

  // Architecture management functions
  const handleStartFromScratch = async () => {
    setIsDesignMode(true)
    setNodes([])
    setEdges([])
    setSelectedNode(null)
    setArchitectureName('Untitled Architecture')

    // Clear all VPCs when starting from scratch
    try {
      await vpcApi.clearAllVPCs()
    } catch (error) {
      console.error('Error clearing VPCs:', error)
    }

    toast({
      title: "Canvas Ready",
      description: "You can now start designing your architecture by dragging components to the canvas.",
    })
  }

  const handleLoadTemplate = () => {
    setShowTemplateSelector(true)
  }

  const handleTemplateSelect = useCallback((architecture: Architecture) => {
    setNodes(architecture.nodes)
    setEdges(architecture.edges)
    setArchitectureName(architecture.name)
    setIsDesignMode(true)
    setShowTemplateSelector(false)
    setSelectedNode(null)

    toast({
      title: "Template Loaded",
      description: `${architecture.name} template has been loaded successfully.`,
    })
  }, [toast])

  const handleArchitectureFromImage = useCallback((architectureData: any) => {
    console.log('🖼️ Loading architecture from image:', architectureData)

    try {
      // Convert the processed image data to architecture format
      const convertedData = imageProcessingService.convertToArchitectureFormat(architectureData)

      console.log('🔄 Converted data:', convertedData)
      console.log('📊 Converted nodes:', convertedData.nodes)
      console.log('🔗 Converted edges:', convertedData.edges)

      if (convertedData.nodes && convertedData.edges) {
        console.log(`✅ Setting ${convertedData.nodes.length} nodes and ${convertedData.edges.length} edges`)

        // Log node IDs for debugging
        console.log('Node IDs:', convertedData.nodes.map(n => n.id))
        console.log('Edge connections:', convertedData.edges.map(e => `${e.source} -> ${e.target}`))

        // 🎯 PRESERVE SPATIAL ARRANGEMENT FROM LOAD IMAGE - NO LAYOUT OPTIMIZATION
        // The PyQt5 MapleGUI preserves the original image layout, so we should too
        console.log('🖼️ Load Image: Preserving spatial arrangement from source image - NO layout optimization applied')
        setNodes(convertedData.nodes)
        setEdges(convertedData.edges)
        setArchitectureName('Architecture from Image')
        setIsDesignMode(true)
        setSelectedNode(null)

        // Trigger custom fit view for better zoom level
        setTriggerFitView(true)
        setTimeout(() => setTriggerFitView(false), 1000)

        toast({
          title: "Image Processed Successfully",
          description: `Loaded ${convertedData.nodes.length} services with ${convertedData.edges.length} connections`,
        })
      } else {
        throw new Error('Invalid architecture data format')
      }
    } catch (error) {
      console.error('❌ Error loading architecture from image:', error)
      toast({
        title: "Error Loading Architecture",
        description: "Failed to load architecture from processed image",
        variant: "destructive"
      })
    }

    setShowLoadImageDialog(false)
  }, [toast])

  const handleClearCanvas = async () => {
    setIsDesignMode(false)
    setNodes([])
    setEdges([])
    setSelectedNode(null)

    // Clear all VPCs when clearing canvas
    try {
      await vpcApi.clearAllVPCs()
    } catch (error) {
      console.error('Error clearing VPCs:', error)
    }

    toast({
      title: "Canvas Cleared",
      description: "All components have been removed from the canvas.",
    })
  }

  // Optimization handlers
  const handleOptimizeService = useCallback((serviceName: string) => {
    setOptimizationTargetService(serviceName)
    setShowOptimizationDialog(true)
  }, [])

  const handleOptimizationApplied = useCallback((recommendation: any) => {
    // Apply optimization recommendation to the architecture
    if (recommendation.action === 'replace_service') {
      // Find the node with the target service and update it
      setNodes(prevNodes =>
        prevNodes.map(node => {
          if (node.data.service?.name === recommendation.current_service) {
            return {
              ...node,
              data: {
                ...node.data,
                service: {
                  ...node.data.service,
                  name: recommendation.recommended_service
                },
                label: recommendation.recommended_service
              }
            }
          }
          return node
        })
      )
    } else if (recommendation.action === 'optimize_configuration') {
      // Update configuration for the target service
      setNodes(prevNodes =>
        prevNodes.map(node => {
          if (node.data.service?.name === recommendation.current_service) {
            return {
              ...node,
              data: {
                ...node.data,
                config: {
                  ...node.data.config,
                  optimized: true,
                  recommendation: recommendation.recommendation_text
                }
              }
            }
          }
          return node
        })
      )
    }
  }, [])

  const handleSaveToDatabase = async () => {
    if (nodes.length === 0) {
      toast({
        title: "Nothing to Save",
        description: "Add some components to your architecture before saving.",
        variant: "destructive"
      })
      return
    }

    try {
      // Prepare architecture data for database storage
      const architecture = {
        name: architectureName,
        nodes,
        edges,
        metadata: {
          totalCost: nodes.reduce((sum, node) => sum + (node.data.cost || 0), 0),
          totalLatency: Math.max(...nodes.map(node => node.data.latency || 0)),
          archType: 'Microservices', // Default architecture type
          createdAt: new Date().toISOString(),
          description: `Architecture with ${nodes.length} components and ${edges.length} connections`
        }
      }

      console.log('Saving architecture to database:', architecture)

      // Save to database using authenticated API
      const result = await architectureApi.saveToDatabase(architecture)

      toast({
        title: "Architecture Saved",
        description: result.message,
      })

      console.log('Architecture saved successfully:', result)
    } catch (error) {
      console.error('Error saving architecture to database:', error)
      toast({
        title: "Save Failed",
        description: error instanceof Error ? error.message : "Failed to save architecture to database.",
        variant: "destructive"
      })
    }
  }

  const handleExportPickle = async () => {
    if (nodes.length === 0) {
      toast({
        title: "Nothing to Export",
        description: "Add some components to your architecture before exporting.",
        variant: "destructive"
      })
      return
    }

    try {
      // Prepare architecture data for PyQt5 MapleGUI compatibility
      const architecture = {
        name: architectureName,
        nodes,
        edges,
        metadata: {
          totalCost: nodes.reduce((sum, node) => sum + (node.data.cost || 0), 0),
          totalLatency: Math.max(...nodes.map(node => node.data.latency || 0)),
          archType: 'Microservices', // Default architecture type
          createdAt: new Date().toISOString()
        }
      }

      console.log('Exporting architecture to PyQt5 pickle format:', architecture)
      console.log('Current nodes:', nodes)
      console.log('Current edges:', edges)
      console.log('Edges being sent:', architecture.edges)

      // Send to backend for pickle conversion
      const response = await fetch('/api/v1/architecture/export-pickle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(architecture)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }))
        throw new Error(errorData.detail || 'Failed to export architecture')
      }

      // Get the pickle file as blob
      const blob = await response.blob()

      // Generate filename
      const safeName = architectureName.replace(/[^a-z0-9]/gi, '_').toLowerCase()
      const filename = `${safeName}.pkl`

      // Try to use File System Access API for "Save As" dialog (modern browsers)
      if (useFileSystemAPI && 'showSaveFilePicker' in window) {
        try {
          const fileHandle = await (window as any).showSaveFilePicker({
            suggestedName: filename,
            types: [{
              description: 'PyQt5 MapleGUI Pickle files',
              accept: { 'application/octet-stream': ['.pkl'] }
            }]
          })

          const writable = await fileHandle.createWritable()
          await writable.write(blob)
          await writable.close()

          toast({
            title: "Architecture Exported",
            description: `Your architecture has been exported as ${filename}`,
          })
          return
        } catch (error) {
          // User cancelled or error occurred, fall back to default download
          console.log('File System Access API failed, using fallback download')
        }
      }

      // Fallback: Use helper function for download
      downloadFile(blob, filename, "Your architecture has been exported as a PyQt5 MapleGUI-compatible .pkl file.")
    } catch (error) {
      console.error('Error exporting architecture:', error)
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export architecture.",
        variant: "destructive"
      })
    }
  }

  const handleExportJson = async () => {
    if (nodes.length === 0) {
      toast({
        title: "Nothing to Export",
        description: "Add some components to your architecture before exporting.",
        variant: "destructive"
      })
      return
    }

    try {
      // Prepare architecture data for JSON export
      const architecture = {
        name: architectureName,
        nodes,
        edges,
        metadata: {
          totalCost: nodes.reduce((sum, node) => sum + (node.data.cost || 0), 0),
          totalLatency: Math.max(...nodes.map(node => node.data.latency || 0)),
          archType: 'Microservices',
          createdAt: new Date().toISOString(),
          version: '1.0'
        }
      }

      console.log('Exporting architecture to JSON format:', architecture)

      // Send to backend for JSON conversion
      const response = await fetch('/api/v1/architecture/export-json', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(architecture)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }))
        throw new Error(errorData.detail || 'Failed to export architecture')
      }

      const result = await response.json()

      // Generate filename
      const safeName = architectureName.replace(/[^a-z0-9]/gi, '_').toLowerCase()
      const filename = `${safeName}.json`

      // Try to use File System Access API for "Save As" dialog (modern browsers)
      if (useFileSystemAPI && 'showSaveFilePicker' in window) {
        try {
          const fileHandle = await (window as any).showSaveFilePicker({
            suggestedName: filename,
            types: [{
              description: 'JSON files',
              accept: { 'application/json': ['.json'] }
            }]
          })

          const writable = await fileHandle.createWritable()
          await writable.write(result.data)
          await writable.close()

          toast({
            title: "Architecture Exported",
            description: `Your architecture has been exported as ${filename}`,
          })
          return
        } catch (error) {
          // User cancelled or error occurred, fall back to default download
          console.log('File System Access API failed, using fallback download')
        }
      }

      // Fallback: Use helper function for download
      const blob = new Blob([result.data], { type: 'application/json' })
      downloadFile(blob, filename, "Your architecture has been exported as a JSON file.")
    } catch (error) {
      console.error('Error exporting architecture:', error)
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export architecture.",
        variant: "destructive"
      })
    }
  }

  const handleLoadPickleFile = async (file: File) => {
    // Send pickle file to backend for processing
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch('/api/v1/architecture/load-pickle', {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error('Failed to process pickle file')
    }

    const architectureData = await response.json()
    await loadArchitectureFromData(architectureData)
  }

  const handleLoadJsonFile = async (file: File) => {
    const text = await file.text()
    const architectureData = JSON.parse(text)
    await loadArchitectureFromData(architectureData)
  }

  const loadArchitectureFromData = async (data: any) => {
    try {
      // Clear current canvas
      setNodes([])
      setEdges([])
      setSelectedNode(null)

      console.log('ArchitectureDesigner: Loading architecture data:', data)

      // Convert loaded data to React Flow format
      const { nodes: loadedNodes, edges: loadedEdges } = await convertMapleGUIToReactFlow(data)

      console.log(`ArchitectureDesigner: Converted to ${loadedNodes.length} nodes and ${loadedEdges.length} edges`)
      console.log('Converted nodes:', loadedNodes)
      console.log('Converted edges:', loadedEdges)

      // For loaded architectures, preserve original positions and connections
      // Check if we have meaningful position data (not all zeros or very close together)
      const positionVariance = loadedNodes.reduce((variance, node, index) => {
        if (index === 0) return 0
        const prevNode = loadedNodes[index - 1]
        return variance + Math.abs(node.position.x - prevNode.position.x) + Math.abs(node.position.y - prevNode.position.y)
      }, 0)

      const hasValidPositions = positionVariance > 50 // Threshold for meaningful position differences

      console.log(`Position analysis: variance=${positionVariance}, hasValidPositions=${hasValidPositions}`)
      console.log('Loaded node positions:', loadedNodes.map(n => ({ id: n.id, label: n.data.label, x: n.position.x, y: n.position.y })))

      let finalNodes = loadedNodes
      let finalEdges = loadedEdges

      if (!hasValidPositions) {
        // Only optimize if positions are invalid (all zeros or too close together)
        console.log('No valid positions found, applying layout optimization')
        const { nodes: optimizedNodes, edges: optimizedEdges } = optimizeArchitectureLayout(loadedNodes, loadedEdges)
        finalNodes = optimizedNodes
        finalEdges = optimizedEdges
      } else {
        console.log('✅ Preserving original positions from loaded architecture - NO layout optimization')
        // Preserve original positions completely - no overlap resolution to avoid rearrangement
        finalNodes = loadedNodes
        finalEdges = loadedEdges
      }

      // Set the loaded architecture
      setNodes(finalNodes)
      setEdges(finalEdges)
      setIsDesignMode(true)
      setArchitectureName(data.name || 'Loaded Architecture')

      toast({
        title: "Architecture Loaded",
        description: `Architecture loaded successfully with ${loadedNodes.length} services and ${loadedEdges.length} connections.`,
      })
    } catch (error) {
      console.error('Error converting architecture data:', error)
      throw new Error('Failed to convert architecture data to React Flow format')
    }
  }

  const handleLoad = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.pkl,.json'
    input.onchange = async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        if (file.name.endsWith('.pkl')) {
          // Handle PyQt5 MapleGUI pickle files
          await handleLoadPickleFile(file)
        } else if (file.name.endsWith('.json')) {
          // Handle JSON architecture files
          await handleLoadJsonFile(file)
        } else {
          throw new Error('Unsupported file format. Please select a .pkl or .json file.')
        }
      } catch (error) {
        console.error('Error loading architecture:', error)
        toast({
          title: "Load Error",
          description: error instanceof Error ? error.message : "Failed to load architecture file.",
          variant: "destructive"
        })
      }
    }
    input.click()
  }



  // Node management functions
  const handleAddNode = useCallback((service: CloudService, position: { x: number; y: number }) => {
    const nodeType = service.provider === 'AWS' ? 'awsService' :
                     service.provider === 'GCP' ? 'gcpService' :
                     service.provider === 'Azure' ? 'azureService' : 'serviceNode'

    const newNode: ArchitectureNode = {
      id: `${service.id}-${Date.now()}`,
      type: nodeType,
      position,
      data: {
        service,
        config: { ...service.defaultConfig },
        label: service.name
      },
      // Add React Flow required properties with compact dimensions
      selected: false,
      dragging: false,
      width: 64,  // 16 * 4 (w-16 in Tailwind)
      height: 64, // 16 * 4 (h-16 in Tailwind)
      zIndex: 0,
      hidden: false,
      deletable: true,
      selectable: true,
      connectable: true,
      focusable: true
    }

    setNodes(prev => [...prev, newNode])
    setSelectedNode(newNode)
  }, [])

  const handleAddUser = useCallback(() => {
    const newNode: ArchitectureNode = {
      id: `user-${Date.now()}`,
      type: 'userNode',
      position: { x: 100, y: 100 },
      data: {
        service: {
          id: 'user',
          name: 'User',
          provider: 'AWS' as const,
          category: 'Compute' as const,
          icon: '👤',
          description: 'End users or clients',
          defaultConfig: {},
          costModel: '',
          latencyModel: '',
          color: '#8B5CF6'
        },
        config: {
          userCount: 1000,
          location: 'Global'
        },
        label: 'Users'
      },
      // Add React Flow required properties with compact dimensions
      selected: false,
      dragging: false,
      width: 64,  // 16 * 4 (w-16 in Tailwind)
      height: 64, // 16 * 4 (h-16 in Tailwind)
      zIndex: 0,
      hidden: false,
      deletable: true,
      selectable: true,
      connectable: true,
      focusable: true
    }

    setNodes(prev => [...prev, newNode])
    setSelectedNode(newNode)

    toast({
      title: "User Component Added",
      description: "User component has been added to your architecture.",
    })
  }, [toast])

  const handleUpdateNode = useCallback((nodeId: string, updates: Partial<ArchitectureNode>) => {
    setNodes(prev => prev.map(node =>
      node.id === nodeId ? { ...node, ...updates } : node
    ))

    // Update selected node if it's the one being updated
    if (selectedNode?.id === nodeId) {
      setSelectedNode(prev => prev ? { ...prev, ...updates } : null)
    }
  }, [selectedNode])

  const handleDeleteNode = useCallback((nodeId: string) => {
    setNodes(prev => prev.filter(node => node.id !== nodeId))
    setEdges(prev => prev.filter(edge => edge.source !== nodeId && edge.target !== nodeId))

    if (selectedNode?.id === nodeId) {
      setSelectedNode(null)
    }

    toast({
      title: "Component Removed",
      description: "Component has been removed from the architecture.",
    })
  }, [selectedNode, toast])

  const handleCalculateCost = useCallback(async (nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId)
    if (!node) return

    try {
      let result
      const config = node.data.config

      switch (node.data.service.id) {
        case 'aws-lambda':
          result = await mlApi.predictLambdaCost({
            workload_invocations: config.workload || 10,  // Use current workload value
            memory_mb: config.memory_mb || 1024,  // Use current memory_mb value
            function_purpose: config.function_defn || config.function_purpose || 'DTS_deepreader',  // Use current function definition
            memory_required: config.memory_required || 204  // Use current memory_required value
          })
          break

        case 'aws-s3':
          result = await mlApi.predictS3Cost({
            workload: config.workload || 10,  // Use current workload value
            file_size: config.fileSize || 100,  // Use current fileSize value
            memory: config.memoryConfig || config.memory || 1024,  // Use current memory configuration
            operation: config.operation || 'read'  // Use current operation value
          })
          break

        case 'aws-dynamodb':
          result = await mlApi.predictDynamoDBCost({
            workload: config.workload || 1,  // Use current workload value
            data_size: config.data_size || 10,  // Use current data_size value
            mem_config: config.mem_config || 8,  // Use current mem_config value
            chunk_size: config.chunk_size || 14,  // Use current chunk_size value
            num_tables: config.num_tables || 1,  // Use current num_tables value
            num_threads: config.num_threads || 1  // Use current num_threads value
          })
          break

        case 'aws-api-gateway':
          // Use PyQt5 exact parameter names from properties panel
          result = await mlApi.predictAPIGatewayCost({
            requests_per_hour: config['Requests(/hour)'] || config.requestsPerHour || 1000,  // Use current requests per hour
            input_payload_size_kb: config['Input payload size(KB)'] || config.inputPayloadSize || 10,  // Use current input payload size
            output_payload_size_kb: config['Output payload size(KB)'] || config.outputPayloadSize || 10  // Use current output payload size
          })
          break

        case 'aws-ec2':
          result = await mlApi.predictEC2Cost({
            instanceType: config.instanceType || 'Inferentia(Inf2.24xlarge)',  // Use current instance type
            LLMModel: config.LLMModel || 'llama_model_7b',  // Use current LLM model
            batchSize: config.batchSize || '1',  // Use current batch size
            inputTokens: config.input_token || config.inputTokens || 50,  // Use current input tokens
            outputTokens: config.output_token || config.outputTokens || 150  // Use current output tokens
          })
          break

        // Additional AWS Services - Use Excel worksheet method for services without ML functions
        case 'amazon-athena':
        case 'amazon-cloudsearch':
        case 'amazon-elasticsearch-service':
        case 'amazon-emr':
        case 'amazon-finspace':
        case 'amazon-managed-streaming-for-apache-kafka':
        case 'amazon-quicksight':
        case 'aws-data-exchange':
        case 'aws-data-pipeline':
        case 'aws-glue':
        case 'aws-lake-formation':
        case 'aws-step-functions':
        case 'amazon-appflow':
        case 'amazon-eventbridge':
        case 'amazon-managed-workflows-for-apache-airflow':
        case 'amazon-mq':
        case 'amazon-simple-notification-service':
        case 'amazon-simple-queue-service':
        case 'amazon-appsync':
        case 'amazon-managed-blockchain':
        case 'amazon-quantum-ledger-database-qldb':
        case 'alexa-for-business':
        case 'amazon-chime':
        case 'amazon-honeycode':
        case 'amazon-workdocs':
        case 'amazon-workmail':
        case 'aws-cost-explorer':
        case 'aws-budgets':
        case 'aws-cost-and-usage-report':
        case 'reserved-instance-reporting':
        case 'savings-plans':
        case 'amazon-ec2-auto-scaling':
        case 'aws-app-runner':
        case 'aws-batch':
        case 'aws-elastic-beanstalk':
        case 'aws-outposts':
        case 'aws-serverless-application-repository':
        case 'aws-snow-family':
        case 'aws-wavelength':
        case 'vmware-cloud-on-aws':
        case 'amazon-elastic-container-registry':
        case 'amazon-elastic-container-service-ecs':
        case 'amazon-ecs-anywhere':
        case 'amazon-elastic-kubernetes-service':
        case 'amazon-eks-distro':
        case 'aws-app2container':
        case 'aws-copilot':
        case 'red-hat-openshift-service-on-aws':
        case 'amazon-connect':
        case 'amazon-pinpoint':
        case 'amazon-simple-email-service':
        case 'amazon-aurora':
        case 'amazon-documentdb':
        case 'amazon-elasticache':
        case 'amazon-keyspaces':
        case 'amazon-neptune':
        case 'amazon-quantum-ledger-database':
        case 'amazon-rds':
        case 'amazon-rds-on-vmware':
        case 'amazon-timestream':
        case 'amazon-database-migration-service':
        case 'aws-developer-tools':
        case 'amazon-codeguru':
        case 'amazon-correto':
        case 'aws-cloud-development-kit':
        case 'aws-cloud9':
        case 'aws-cloudshell':
        case 'aws-codeartifact':
        case 'aws-codebuild':
        case 'aws-codecommit':
        case 'aws-codedeploy':
        case 'aws-codepipeline':
        case 'aws-codestar':
        case 'aws-command-line-interface':
        case 'aws-device-farm':
        case 'aws-fault-injection-simulator':
        case 'aws-tools-and-sdks':
        case 'aws-x-ray':
        case 'amazon-appstream-2-0':
        case 'amazon-worklink':
        case 'amazon-workspaces':
        case 'aws-amplify':
        case 'amazon-location-service':
        case 'amazon-gamelift':
        case 'amazon-lumberyard':
        case 'aws-iot-1-click':
        case 'aws-iot-analytics':
        case 'aws-iot-button':
        case 'aws-iot-device-defender':
        case 'aws-iot-management':
        case 'aws-iot-events':
        case 'aws-iot-sitewise':
        case 'aws-iot-things-graph':
        case 'aws-partner-device-catalog':
        case 'freertos':
        case 'amazon-augmented-ai':
        case 'amazon-devops-guru':
        case 'amazon-elastic-inference':
        case 'amazon-forecast':
        case 'amazon-fraud-detector':
        case 'amazon-healthlake':
        case 'amazon-kendra':
        case 'amazon-lex':
        case 'amazon-lookout-for-equipment':
        case 'amazon-lookout-for-metrics':
        case 'amazon-lookout-for-vision':
        case 'amazon-minitron':
        case 'amazon-personalize':
        case 'amazon-polly':
        case 'amazon-sagemaker-data-wrangler':
        case 'amazon-sagemaker-ground-truth':
        case 'amazon-translate':
        case 'amazon-transcribe':
        case 'amazon-deep-learning-amis':
        case 'aws-deep-learning-containers':
        case 'aws-deepcomposer':
        case 'aws-deeplens':
        case 'aws-deepracer':
        case 'aws-inferentia':
        case 'aws-panorama':
        case 'pytorch-on-aws':
        case 'apache-mxnet-on-aws':
        case 'tensorflow-on-aws':
        case 'aws-auto-scaling':
        case 'aws-chatbot':
        case 'aws-cloudformation':
        case 'aws-cloudtrail':
        case 'aws-compute-optimizer':
        case 'aws-config':
        case 'aws-control-tower':
        case 'aws-console-mobile-application':
        case 'aws-distro-for-opentelemetry':
        case 'aws-launch-wizard':
        case 'aws-license-manager':
        case 'aws-management-console':
        case 'amazon-managed-service-for-grafana':
        case 'aws-management-service-for-prometheus':
        case 'aws-opsworks':
        case 'aws-organizations':
        case 'aws-personal-health-dashboard':
        case 'aws-proton':
        case 'aws-service-catalog':
        case 'aws-systems-manager':
        case 'aws-trusted-advisor':
        case 'aws-well-architected-tool':
          // Use Excel worksheet method for cost calculation (same as PyQt5)
          try {
            const worksheetResult = await fetch('/api/ml/calculate-worksheet-cost', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                serviceId: node.data.service.id,
                config: config
              })
            })

            if (worksheetResult.ok) {
              result = await worksheetResult.json()
            } else {
              // Fallback to default values if worksheet calculation fails
              result = {
                cost: 0.1,
                latency: 100,
                message: `Estimated cost for ${node.data.service.name}`
              }
            }
          } catch (error) {
            console.warn(`Worksheet calculation failed for ${node.data.service.id}:`, error)
            // Fallback to default values
            result = {
              cost: 0.1,
              latency: 100,
              message: `Estimated cost for ${node.data.service.name}`
            }
          }
          break

        default:
          // For services without specific cost models, use mock data
          result = {
            cost: Math.random() * 100,
            latency: Math.random() * 1000
          }
      }

      if (result) {
        handleUpdateNode(nodeId, {
          data: {
            ...node.data,
            cost: result.cost,
            latency: result.latency
          }
        })

        toast({
          title: "Cost Calculated",
          description: `Cost: $${result.cost.toFixed(4)}, Latency: ${result.latency.toFixed(2)}ms`,
        })
      }
    } catch (error) {
      console.error('Error calculating cost:', error)
      toast({
        title: "Calculation Failed",
        description: "Failed to calculate cost and latency.",
        variant: "destructive"
      })
    }
  }, [nodes, handleUpdateNode, toast])

  const handleServiceDragStart = useCallback((event: React.DragEvent, service: CloudService) => {
    // This is handled by the ComponentPalette component
  }, [])

  const handleNodeSelect = useCallback((node: ArchitectureNode | null) => {
    setSelectedNode(node)
  }, [])

  const handleCostCalculated = useCallback((analysis: CostAnalysis) => {
    setCostAnalysis(analysis)
    setActiveTab('cost')
  }, [])

  // Calculate total metrics
  const totalCost = useMemo(() => {
    return nodes.reduce((sum, node) => sum + (node.data.cost || 0), 0)
  }, [nodes])

  const maxLatency = useMemo(() => {
    return Math.max(...nodes.map(node => node.data.latency || 0), 0)
  }, [nodes])

  // Create architecture object for cost calculation
  const currentArchitecture: Architecture = useMemo(() => ({
    name: architectureName,
    description: 'Current architecture design',
    nodes,
    edges,
    metadata: {
      totalCost: totalCost,
      totalLatency: maxLatency,
      provider: 'AWS',
      archType: 'Custom',
      createdAt: new Date().toISOString()
    }
  }), [architectureName, nodes, edges, totalCost, maxLatency])

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Architecture Designer</h1>
            <p className="text-sm text-muted-foreground">
              Design and visualize your cloud architecture with professional diagramming tools
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {/* Architecture Metrics */}
            {isDesignMode && nodes.length > 0 && (
              <div className="flex items-center space-x-4 mr-4">
                <div className="text-sm">
                  <span className="text-muted-foreground">Components:</span>
                  <span className="font-medium ml-1">{nodes.length}</span>
                </div>
                {totalCost > 0 && (
                  <div className="text-sm">
                    <span className="text-muted-foreground">Total Cost:</span>
                    <span className="font-medium ml-1 text-green-600">${totalCost.toFixed(4)}</span>
                  </div>
                )}
                {maxLatency > 0 && (
                  <div className="text-sm">
                    <span className="text-muted-foreground">Max Latency:</span>
                    <span className="font-medium ml-1 text-blue-600">{maxLatency.toFixed(2)}ms</span>
                  </div>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <Button variant="outline" size="sm" onClick={handleLoad}>
              <Upload className="h-4 w-4 mr-2" />
              Load
            </Button>
            <Button variant="outline" size="sm" onClick={handleSaveToDatabase}>
              <Save className="h-4 w-4 mr-2" />
              Save to Cloud
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleExportPickle}>
                  <Download className="h-4 w-4 mr-2" />
                  Export as PyQt5 (.pkl)
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleExportJson}>
                  <Download className="h-4 w-4 mr-2" />
                  Export as JSON (.json)
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuCheckboxItem
                  checked={useFileSystemAPI}
                  onCheckedChange={setUseFileSystemAPI}
                >
                  Show "Save As" dialog
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>
            {isDesignMode && (
              <>
                <Button variant="outline" size="sm" onClick={() => setShowOptimizationDialog(true)}>
                  <Zap className="h-4 w-4 mr-2" />
                  Optimize
                </Button>
                <Button variant="outline" size="sm" onClick={handleClearCanvas}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Main Content - Optimized Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Optimized Component Palette */}
        <div className="w-72 h-full border-r border-gray-200 bg-white shadow-sm">
          <ComponentPalette
            onServiceDragStart={handleServiceDragStart}
            onAddUser={handleAddUser}
            className="h-full"
          />
        </div>

        {/* Enhanced Canvas Area */}
        <div className="flex-1 flex flex-col bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 relative">
          {/* Canvas Background Pattern */}
          <div className="absolute inset-0 opacity-30" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.15) 1px, transparent 0)`,
            backgroundSize: '20px 20px'
          }}></div>

          {showTemplateSelector ? (
            <div className="flex-1 overflow-y-auto p-8 relative z-10">
              <div className="max-w-6xl mx-auto">
                <TemplateSelector
                  onTemplateSelect={handleTemplateSelect}
                  onClose={() => setShowTemplateSelector(false)}
                />
              </div>
            </div>
          ) : !isDesignMode ? (
            <div className="flex-1 flex items-center justify-center relative z-10">
              <div className="text-center space-y-8 max-w-2xl px-8">
                <div className="relative">
                  <div className="w-32 h-32 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl">
                    <Network className="h-16 w-16 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full animate-bounce"></div>
                </div>
                <div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Professional Architecture Designer
                  </h3>
                  <p className="text-lg text-gray-600 leading-relaxed mb-8">
                    Create stunning cloud architecture diagrams with our advanced drag-and-drop interface.
                    Design complex systems with real-time cost analysis and seamless component connections.
                  </p>
                </div>
                <div className="flex space-x-4 justify-center">
                  {/* <Button
                    variant="outline"
                    onClick={handleLoadTemplate}
                    className="px-6 py-3 text-base border-2 border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400 transition-all duration-200"
                  >
                    <FileText className="h-5 w-5 mr-2" />
                    Load Template
                  </Button> */}
                  <Button
                    variant="outline"
                    onClick={() => setShowLoadImageDialog(true)}
                    className="px-6 py-3 text-base border-2 border-green-300 text-green-700 hover:bg-green-50 hover:border-green-400 transition-all duration-200"
                  >
                    <FileImage className="h-5 w-5 mr-2" />
                    Load from Image
                  </Button>
                  <Button
                    onClick={handleStartFromScratch}
                    className="px-6 py-3 text-base bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <Zap className="h-5 w-5 mr-2" />
                    Start Designing
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex-1 relative z-10">
              <ArchitectureCanvas
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                onNodeSelect={handleNodeSelect}
                onAddNode={handleAddNode}
                className="h-full w-full"
                triggerFitView={triggerFitView}
              />
            </div>
          )}
        </div>

        {/* Compact Properties Panel */}
        <div className="w-80 bg-gradient-to-b from-gray-50 to-white border-l border-gray-200 flex flex-col shadow-lg">
          {/* Enhanced Tab Navigation */}
          {isDesignMode && (
            <div className="bg-white border-b border-gray-200 p-2">
              <div className="flex bg-gray-100 rounded-xl p-1">
                <button
                  onClick={() => setActiveTab('properties')}
                  className={`flex-1 px-4 py-3 text-sm font-semibold rounded-lg transition-all duration-200 ${
                    activeTab === 'properties'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                  }`}
                >
                  Properties
                </button>
                <button
                  onClick={() => setActiveTab('cost')}
                  className={`flex-1 px-4 py-3 text-sm font-semibold rounded-lg transition-all duration-200 ${
                    activeTab === 'cost'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                  }`}
                >
                  Cost Analysis
                </button>
                <button
                  onClick={() => setActiveTab('selective')}
                  className={`flex-1 px-4 py-3 text-sm font-semibold rounded-lg transition-all duration-200 ${
                    activeTab === 'selective'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                  }`}
                >
                  Selective
                </button>
                <button
                  onClick={() => setActiveTab('vpc')}
                  className={`flex-1 px-4 py-3 text-sm font-semibold rounded-lg transition-all duration-200 ${
                    activeTab === 'vpc'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                  }`}
                >
                  VPC
                </button>
              </div>
            </div>
          )}

          {/* Panel Content */}
          <div className="flex-1 overflow-hidden">
            {!isDesignMode ? (
              <div className="h-full flex items-center justify-center p-6">
                <div className="text-center text-gray-500">
                  <p className="text-sm">Start designing to access properties and cost analysis</p>
                </div>
              </div>
            ) : activeTab === 'properties' ? (
              <PropertiesPanel
                selectedNode={selectedNode}
                onUpdateNode={handleUpdateNode}
                onDeleteNode={handleDeleteNode}
                onCalculateCost={handleCalculateCost}
                onOptimizeService={handleOptimizeService}
                className="h-full"
              />
            ) : activeTab === 'cost' ? (
              <div className="h-full overflow-y-auto p-4">
                <CostCalculationPanel
                  architecture={currentArchitecture}
                  onCostCalculated={handleCostCalculated}
                />
              </div>
            ) : activeTab === 'selective' ? (
              <SelectiveCostCalculation
                architecture={currentArchitecture}
                onCostCalculated={(results) => {
                  toast({
                    title: "Selective Cost Calculated",
                    description: `Calculated costs for ${results.serviceCount} selected services. Total: $${results.totalCost.toFixed(4)}`,
                  })
                }}
              />
            ) : (
              <div className="h-full overflow-y-auto p-4">
                <VPCManagement
                  nodes={nodes}
                  className="h-full"
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Load Image Dialog */}
      <LoadImageDialog
        open={showLoadImageDialog}
        onOpenChange={setShowLoadImageDialog}
        onArchitectureLoaded={handleArchitectureFromImage}
      />

      {/* Optimization Dialog */}
      <OptimizationDialog
        isOpen={showOptimizationDialog}
        onClose={() => setShowOptimizationDialog(false)}
        selectedService={optimizationTargetService}
        nodes={nodes}
        edges={edges}
        onOptimizationApplied={handleOptimizationApplied}
      />
    </div>
  )
}

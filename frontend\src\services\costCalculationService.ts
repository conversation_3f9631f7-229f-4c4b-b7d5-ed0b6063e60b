import { Architecture, ArchitectureNode, CostAnalysis } from '@/types/architecture'
import { worksheetService } from './worksheetService'

// Cost calculation service that integrates with the ML API
export class CostCalculationService {
  private baseUrl: string

  constructor(baseUrl: string = 'http://localhost:8000') {
    this.baseUrl = baseUrl
  }

  /**
   * Calculate cost and latency for the entire architecture
   */
  async calculateArchitectureCost(architecture: Architecture): Promise<CostAnalysis> {
    try {
      const requestData = this.formatArchitectureForBackend(architecture)

      const response = await fetch(`${this.baseUrl}/api/v1/ml/calculate-cost`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('API Error:', errorText)
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      return this.formatCostAnalysisResponse(result)
    } catch (error) {
      console.error('Error calculating architecture cost:', error)
      // Return fallback calculation using worksheet values
      return await this.calculateFallbackCost(architecture)
    }
  }

  /**
   * Calculate cost for a single service node
   */
  async calculateServiceCost(node: ArchitectureNode): Promise<{ cost: number; latency: number }> {
    try {
      const response = await fetch(`${this.baseUrl}/calculate-service-cost`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          service: node.data.service,
          config: node.data.config
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      return {
        cost: result.cost || 0,
        latency: result.latency || 0
      }
    } catch (error) {
      console.error('Error calculating service cost, using worksheet fallback:', error)
      // Return fallback calculation using worksheet values (matches PyQt5 behavior)
      return await this.calculateFallbackServiceCost(node)
    }
  }

  /**
   * Get cost predictions using ML models
   */
  async getPredictions(serviceType: string, config: Record<string, any>): Promise<{ cost: number; latency: number }> {
    try {
      const response = await fetch(`${this.baseUrl}/predict`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          service_type: serviceType,
          configuration: config
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      return {
        cost: result.predicted_cost || 0,
        latency: result.predicted_latency || 0
      }
    } catch (error) {
      console.error('Error getting ML predictions:', error)
      return { cost: 0, latency: 0 }
    }
  }

  /**
   * Format architecture data for backend API (mimics PyQt5 structure)
   */
  private formatArchitectureForBackend(architecture: Architecture) {
    return {
      architecture_type: "Monolith", // Default, could be made configurable
      architecture: {
        name: architecture.name,
        description: architecture.description,
        uses_vpc: architecture.nodes.some(node => node.data.service.id.includes('vpc')),
        nat_gateways: architecture.nodes.filter(node => node.data.service.id.includes('nat')).length
      },
      nodes: architecture.nodes.map(node => ({
        id: node.id,
        service_id: node.data.service.id,
        service_name: node.data.service.name,
        config: {
          ...node.data.config,
          // Add default values if missing
          workload: node.data.config.workload || 1000,
          memory: node.data.config.memory || 1024
        },
        position: node.position
      })),
      edges: architecture.edges.map(edge => ({
        id: `${edge.source}-${edge.target}`,
        source: edge.source,
        target: edge.target,
        config: {
          data_transfer_gb: 0.1 // Default data transfer
        }
      }))
    }
  }

  /**
   * Format architecture data for API consumption (legacy method)
   */
  private formatArchitectureForAPI(architecture: Architecture) {
    return {
      name: architecture.name,
      description: architecture.description,
      services: architecture.nodes.map(node => ({
        id: node.id,
        service_type: node.data.service.id,
        service_name: node.data.service.name,
        provider: node.data.service.provider,
        category: node.data.service.category,
        configuration: node.data.config,
        cost_model: node.data.service.costModel,
        latency_model: node.data.service.latencyModel
      })),
      connections: architecture.edges.map(edge => ({
        source: edge.source,
        target: edge.target,
        type: edge.type || 'default'
      }))
    }
  }

  /**
   * Format cost analysis response from API (updated for new backend structure)
   */
  private formatCostAnalysisResponse(apiResponse: any): CostAnalysis {
    return {
      totalMonthlyCost: apiResponse.total_cost || 0,
      totalLatency: apiResponse.total_latency || 0,
      breakdown: (apiResponse.service_breakdown || []).map((item: any) => ({
        serviceId: item.service_id,
        serviceName: item.service_name,
        cost: item.cost,
        latency: item.latency,
        percentage: item.percentage
      })),
      recommendations: (apiResponse.recommendations || []).map((rec: string) => ({
        type: 'optimization',
        message: rec,
        impact: 'medium'
      }))
    }
  }

  /**
   * Fallback cost calculation when API is unavailable (uses worksheet values)
   */
  private async calculateFallbackCost(architecture: Architecture): Promise<CostAnalysis> {
    const breakdown = await Promise.all(
      architecture.nodes.map(async (node) => {
        const fallbackCost = await this.calculateFallbackServiceCost(node)
        return {
          serviceId: node.id,
          serviceName: node.data.service.name,
          cost: fallbackCost.cost,
          latency: fallbackCost.latency,
          percentage: 0
        }
      })
    )

    const totalCost = breakdown.reduce((sum, item) => sum + item.cost, 0)
    const totalLatency = breakdown.reduce((sum, item) => sum + item.latency, 0)

    // Calculate percentages
    breakdown.forEach(item => {
      item.percentage = totalCost > 0 ? (item.cost / totalCost) * 100 : 0
    })

    return {
      totalMonthlyCost: totalCost,
      totalLatency: totalLatency,
      breakdown,
      recommendations: [
        {
          type: 'cost',
          message: 'Cost calculation using worksheet values (matches PyQt5 initial values). Connect to ML API for dynamic predictions.',
          impact: 'medium'
        }
      ]
    }
  }

  /**
   * Fallback service cost calculation using worksheet values (matches PyQt5 behavior)
   */
  private async calculateFallbackServiceCost(node: ArchitectureNode): Promise<{ cost: number; latency: number }> {
    const service = node.data.service
    const config = node.data.config

    // First try to get worksheet values (matches PyQt5 initial values)
    try {
      const worksheetValues = await worksheetService.getServiceWorksheetValues(service.id)
      if (worksheetValues) {
        console.log(`Using worksheet values for ${service.id}: cost=${worksheetValues.cost}, latency=${worksheetValues.latency}`)
        return {
          cost: worksheetValues.cost,
          latency: worksheetValues.latency
        }
      }
    } catch (error) {
      console.warn('Failed to get worksheet values, falling back to estimation:', error)
    }

    // Fallback to basic cost estimation based on service type
    let baseCost = 0
    let baseLatency = 0

    switch (service.category) {
      case 'Compute':
        baseCost = this.estimateComputeCost(service.id, config)
        baseLatency = this.estimateComputeLatency(service.id, config)
        break
      case 'Storage':
        baseCost = this.estimateStorageCost(service.id, config)
        baseLatency = this.estimateStorageLatency(service.id, config)
        break
      case 'Database':
        baseCost = this.estimateDatabaseCost(service.id, config)
        baseLatency = this.estimateDatabaseLatency(service.id, config)
        break
      case 'AI/ML':
        baseCost = this.estimateMLCost(service.id, config)
        baseLatency = this.estimateMLLatency(service.id, config)
        break
      case 'Networking':
        baseCost = this.estimateNetworkingCost(service.id, config)
        baseLatency = this.estimateNetworkingLatency(service.id, config)
        break
      case 'Analytics':
        baseCost = this.estimateAnalyticsCost(service.id, config)
        baseLatency = this.estimateAnalyticsLatency(service.id, config)
        break
      default:
        baseCost = 0.000002 // Default fallback cost (per-request) - ~$0.000002 per request
        baseLatency = 50 // Default fallback latency
    }

    return { cost: baseCost, latency: baseLatency }
  }

  // Fallback cost estimation methods - using per-request costs to match PyQt5
  private estimateComputeCost(serviceId: string, config: any): number {
    if (serviceId.includes('lambda')) {
      const memory = config.memory || 1024
      const workload = config.workload || 1000
      return (memory / 1024) * (workload / 1000) * 0.0000000166667 // Per-request estimate
    }
    if (serviceId.includes('ec2')) {
      return 0.000068 // Base EC2 per-request cost
    }
    return 0.000034 // Per-request cost
  }

  private estimateComputeLatency(serviceId: string, config: any): number {
    if (serviceId.includes('lambda')) return 100
    if (serviceId.includes('ec2')) return 50
    return 75
  }

  private estimateStorageCost(serviceId: string, config: any): number {
    const workload = config.workload || 1000
    const fileSize = config.fileSize || 1024
    return (workload * fileSize / 1024 / 1024) * 0.023 / 730 / 1000 // S3 standard pricing (per-request)
  }

  private estimateStorageLatency(serviceId: string, config: any): number {
    if (serviceId.includes('s3')) return 100
    if (serviceId.includes('ebs')) return 10
    return 50
  }

  private estimateDatabaseCost(serviceId: string, config: any): number {
    if (serviceId.includes('dynamodb')) {
      // Use PyQt5 compatible calculation
      const workload = config.workload || 1  // PyQt5 default: 1, not 1000
      const dataSize = config.data_size || 10

      // PyQt5 calculation: write_cost + storage_cost
      const writeRequestCost = 1.25 / 1000000  // PyQt5 WRITE_COST_PER_REQUEST
      const storageGbMonthCost = 1.25  // PyQt5 STORAGE_COST_PER_GB_MONTH

      const totalItemSizeMb = workload * dataSize
      const writeCost = workload * writeRequestCost
      const totalStorageGb = totalItemSizeMb / (1024 * 1024)
      const storageCost = totalStorageGb * storageGbMonthCost

      return (writeCost + storageCost) / 730 / 1000  // Convert to per-request
    }
    return 0.000041 // Per-request cost
  }

  private estimateDatabaseLatency(serviceId: string, config: any): number {
    if (serviceId.includes('dynamodb')) return 10
    if (serviceId.includes('rds')) return 50
    return 30
  }

  private estimateMLCost(serviceId: string, config: any): number {
    if (serviceId.includes('sagemaker')) {
      return 0.000137 // Base SageMaker per-request cost
    }
    if (serviceId.includes('bedrock')) {
      const inputTokens = config.inputTokens || 1000
      const outputTokens = config.outputTokens || 500
      return ((inputTokens * 0.00001) + (outputTokens * 0.00003)) / 730 / 1000 // Bedrock pricing (per-request)
    }
    return 0.000068 // Per-request cost
  }

  private estimateMLLatency(serviceId: string, config: any): number {
    if (serviceId.includes('sagemaker')) return 200
    if (serviceId.includes('bedrock')) return 500
    return 300
  }

  private estimateNetworkingCost(serviceId: string, config: any): number {
    if (serviceId.includes('cloudfront')) {
      const dataTransfer = config.dataTransfer || 100
      return (dataTransfer * 0.085) / 730 / 1000 // CloudFront pricing (per-request)
    }
    return 0.000021 // Per-request cost
  }

  private estimateNetworkingLatency(serviceId: string, config: any): number {
    if (serviceId.includes('cloudfront')) return 20
    if (serviceId.includes('api-gateway')) return 100
    return 50
  }

  private estimateAnalyticsCost(serviceId: string, config: any): number {
    if (serviceId.includes('kinesis')) {
      const shards = config.shards || 1
      return shards * 0.015 / 1000 // Kinesis shard hour pricing (per-request)
    }
    return 0.000055 // Per-request cost
  }

  private estimateAnalyticsLatency(serviceId: string, config: any): number {
    if (serviceId.includes('kinesis')) return 200
    if (serviceId.includes('redshift')) return 1000
    return 500
  }
}

// Export singleton instance
export const costCalculationService = new CostCalculationService()

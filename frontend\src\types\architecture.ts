import React from 'react'

export interface CloudService {
  id: string
  name: string
  provider: 'AWS' | 'GCP' | 'Azure'
  category: 'Compute' | 'Storage' | 'Database' | 'AI/ML' | 'Networking' | 'Analytics' | 'Security' | 'IoT' | 'Developer Tools' | 'Application Integration' | 'Management' | 'Frontend Web & Mobile' | 'Blockchain' | 'Business Applications' | 'Cost Management' | 'User' | 'Management' | 'Frontend Web & Mobile' | 'Blockchain' | 'Business Applications' | 'Cost Management' | 'User'
  icon: string
  fallbackIcon?: string
  description: string
  defaultConfig: Record<string, any>
  costModel: string
  latencyModel: string
  color: string
}

export interface ArchitectureNode {
  id: string
  type: string
  position: { x: number; y: number }
  data: {
    service: CloudService
    config: Record<string, any>
    cost?: number
    latency?: number
    label: string
    originalName?: string // Preserve original service name from PyQt5
  }
  selected?: boolean
  dragging?: boolean
  // Additional React Flow properties
  width?: number
  height?: number
  zIndex?: number
  extent?: 'parent' | [[number, number], [number, number]]
  expandParent?: boolean
  positionAbsolute?: { x: number; y: number }
  hidden?: boolean
  deletable?: boolean
  selectable?: boolean
  connectable?: boolean
  focusable?: boolean
  resizing?: boolean
}

export interface ArchitectureEdge {
  id: string
  source: string
  target: string
  type?: string
  data?: {
    cost?: number
    latency?: number
    bandwidth?: string
    protocol?: string
  }
  animated?: boolean
  style?: Record<string, any>
  // Additional React Flow properties
  sourceHandle?: string | null
  targetHandle?: string | null
  selected?: boolean
  hidden?: boolean
  deletable?: boolean
  selectable?: boolean
  focusable?: boolean
  updatable?: boolean
  label?: string | React.ReactNode
  labelStyle?: Record<string, any>
  labelShowBg?: boolean
  labelBgStyle?: Record<string, any>
  labelBgPadding?: [number, number]
  labelBgBorderRadius?: number
  markerStart?: string
  markerEnd?: string
  pathOptions?: any
  interactionWidth?: number
}

export interface Architecture {
  id?: string
  name: string
  description?: string
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
  metadata?: {
    totalCost?: number
    totalLatency?: number
    provider?: string
    archType?: 'Microservices' | 'Monolith' | 'Serverless' | 'Hybrid'
    createdAt?: string
    updatedAt?: string
  }
}

export interface ServiceCategory {
  name: string
  services: CloudService[]
  icon: string
  color: string
}

export interface CostAnalysis {
  costPerRequest: number
  costPer1000Requests: number
  totalLatency: number  // in seconds
  breakdown: {
    serviceId: string
    serviceName: string
    cost: number  // cost per request
    latency: number  // in seconds
    percentage: number
  }[]
  recommendations?: {
    type: 'cost' | 'performance' | 'reliability'
    message: string
    impact: 'high' | 'medium' | 'low'
  }[]
}

export interface ArchitectureTemplate {
  id: string
  name: string
  description: string
  category: string
  thumbnail: string
  architecture: Architecture
  tags: string[]
}

// Node types for React Flow
export type NodeType = 'awsService' | 'gcpService' | 'azureService' | 'mlModel' | 'userNode' | 'vpcContainer'

// Edge types for React Flow
export type EdgeType = 'default' | 'smoothstep' | 'straight' | 'dataFlow' | 'networkConnection'

export interface DraggedService {
  service: CloudService
  offset: { x: number; y: number }
}

export interface CanvasState {
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
  selectedNodes: string[]
  selectedEdges: string[]
  isConnecting: boolean
  draggedService: DraggedService | null
  zoom: number
  viewport: { x: number; y: number; zoom: number }
}

export interface ServiceConfiguration {
  [key: string]: {
    type: 'text' | 'number' | 'select' | 'boolean' | 'slider'
    label: string
    value: any
    options?: string[] | number[]
    min?: number
    max?: number
    step?: number
    required?: boolean
    description?: string
  }
}

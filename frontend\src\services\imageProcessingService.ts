/**
 * Image Processing Service
 * 
 * Frontend service for handling Load Image functionality,
 * communicating with the FastAPI backend image processing endpoints.
 */

import { ArchitectureNode, ArchitectureEdge } from '@/types/architecture'

export interface ImageProcessingResult {
  task_id: string
  status: 'success' | 'error' | 'processing'
  message: string
  data?: {
    nodes: ArchitectureNode[]
    edges: ArchitectureEdge[]
    adjacency_list: Record<string, string[]>
    service_counts: string[]
    connections: string[]
  }
  metadata?: {
    services_identified: number
    total_service_instances: number
    connections_found: number
    filename: string
  }
}

export interface ProcessingStatus {
  task_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  message: string
  data?: any
}

export interface AWSServicesResponse {
  services: string[]
  total_count: number
}

export interface ServiceValidationResult {
  valid_services: string[]
  invalid_services: string[]
  validation_summary: {
    total_services: number
    valid_count: number
    invalid_count: number
  }
}

class ImageProcessingService {
  private baseUrl = '/api/v1/image-processing'

  /**
   * Get authentication headers
   */
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('access_token')
    return {
      'Authorization': `Bearer ${token}`
    }
  }

  /**
   * Process architecture diagram image
   * 
   * @param file - Image file to process (PNG/JPG/JPEG)
   * @returns Promise with processing result
   */
  async processArchitectureImage(file: File): Promise<ImageProcessingResult> {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch(`${this.baseUrl}/load-image`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      return result as ImageProcessingResult

    } catch (error) {
      console.error('Error processing architecture image:', error)
      throw error
    }
  }

  /**
   * Get processing status for a task
   * 
   * @param taskId - Unique task identifier
   * @returns Promise with processing status
   */
  async getProcessingStatus(taskId: string): Promise<ProcessingStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/processing-status/${taskId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      return result as ProcessingStatus

    } catch (error) {
      console.error('Error getting processing status:', error)
      throw error
    }
  }

  /**
   * Get AWS services catalog
   * 
   * @returns Promise with AWS services list
   */
  async getAWSServicesCatalog(): Promise<AWSServicesResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/aws-services`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      return result as AWSServicesResponse

    } catch (error) {
      console.error('Error getting AWS services catalog:', error)
      throw error
    }
  }

  /**
   * Validate services against AWS catalog
   * 
   * @param services - List of service names to validate
   * @returns Promise with validation results
   */
  async validateServices(services: string[]): Promise<ServiceValidationResult> {
    try {
      const response = await fetch(`${this.baseUrl}/validate-services`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        },
        body: JSON.stringify(services)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      return result.data as ServiceValidationResult

    } catch (error) {
      console.error('Error validating services:', error)
      throw error
    }
  }

  /**
   * Check service health
   * 
   * @returns Promise with health status
   */
  async checkHealth(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      return result

    } catch (error) {
      console.error('Error checking image processing service health:', error)
      throw error
    }
  }

  /**
   * Convert processed image data to architecture format
   * 
   * This method ensures compatibility with the existing architecture designer
   * by converting the backend response to the expected frontend format.
   * 
   * @param imageData - Processed image data from backend
   * @returns Converted architecture data
   */
  convertToArchitectureFormat(imageData: any): {
    nodes: ArchitectureNode[]
    edges: ArchitectureEdge[]
  } {
    try {
      // The backend already returns data in React Flow format
      const { nodes, edges } = imageData

      // Ensure nodes have the correct structure
      const convertedNodes: ArchitectureNode[] = nodes.map((node: any) => ({
        id: node.id,
        type: node.type || 'serviceNode',
        position: node.position || { x: 0, y: 0 },
        data: {
          service: node.data.service,
          config: node.data.config || {},
          label: node.data.label,
          // Don't set default cost/latency - let them be undefined to trigger calculation
          ...(node.data.cost !== null && node.data.cost !== undefined && { cost: node.data.cost }),
          ...(node.data.latency !== null && node.data.latency !== undefined && { latency: node.data.latency })
        },
        // Add all required React Flow properties
        selected: node.selected || false,
        dragging: node.dragging || false,
        width: node.width || 64,
        height: node.height || 64,
        zIndex: node.zIndex || 0,
        hidden: node.hidden || false,
        deletable: node.deletable !== false,
        selectable: node.selectable !== false,
        connectable: node.connectable !== false,
        focusable: node.focusable !== false
      }))

      // Ensure edges have the correct structure
      const convertedEdges: ArchitectureEdge[] = edges.map((edge: any) => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        type: edge.type || 'step',
        markerEnd: edge.markerEnd || {
          type: 'arrowclosed',
          color: '#4F46E5',
          width: 20,
          height: 20
        },
        style: edge.style || {
          stroke: '#4F46E5',
          strokeWidth: 2.5
        },
        // Add all required React Flow edge properties
        animated: edge.animated || false,
        sourceHandle: edge.sourceHandle || null,
        targetHandle: edge.targetHandle || null,
        selected: edge.selected || false,
        hidden: edge.hidden || false,
        deletable: edge.deletable !== false,
        selectable: edge.selectable !== false,
        focusable: edge.focusable !== false
      }))

      return {
        nodes: convertedNodes,
        edges: convertedEdges
      }

    } catch (error) {
      console.error('Error converting image data to architecture format:', error)
      throw new Error('Failed to convert processed image data')
    }
  }

  /**
   * Validate image file before upload
   * 
   * @param file - File to validate
   * @returns Validation result
   */
  validateImageFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg']
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Only PNG and JPEG images are supported.'
      }
    }

    // Check file size (20MB limit)
    const maxSize = 20 * 1024 * 1024 // 20MB
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File too large. Maximum size is 20MB.'
      }
    }

    return { valid: true }
  }

  /**
   * Generate unique task ID for tracking
   * 
   * @returns Unique task identifier
   */
  generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// Export singleton instance
export const imageProcessingService = new ImageProcessingService()
export default imageProcessingService

"""
Architecture Chat API Endpoints

Provides AI-powered chat functionality for architecture analysis,
optimization suggestions, and general questions about cloud architectures.
"""

from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field

from app.auth.dependencies import get_current_user
from app.database.models import User
from app.services.architecture_chat_service import architecture_chat_service

router = APIRouter()

# Request Models
class ArchitectureChatRequest(BaseModel):
    """Request model for architecture chat"""
    user_query: str = Field(..., description="User's question or request")
    architecture_data: Optional[Dict[str, Any]] = Field(None, description="Current architecture data (nodes, edges)")
    conversation_id: Optional[str] = Field(None, description="Optional conversation identifier")
    
    class Config:
        schema_extra = {
            "example": {
                "user_query": "How can I optimize the cost of my current architecture?",
                "architecture_data": {
                    "nodes": [
                        {
                            "id": "ec2-1",
                            "data": {
                                "service": {"name": "Amazon EC2"},
                                "config": {"instanceType": "t3.medium"}
                            }
                        }
                    ],
                    "edges": []
                },
                "conversation_id": "chat_123"
            }
        }

class ArchitectureInsightsRequest(BaseModel):
    """Request model for architecture insights"""
    architecture_data: Dict[str, Any] = Field(..., description="Architecture data to analyze")
    
    class Config:
        schema_extra = {
            "example": {
                "architecture_data": {
                    "nodes": [
                        {
                            "id": "ec2-1",
                            "data": {
                                "service": {"name": "Amazon EC2"},
                                "config": {"instanceType": "t3.medium"}
                            }
                        },
                        {
                            "id": "s3-1", 
                            "data": {
                                "service": {"name": "Amazon S3"},
                                "config": {"storageClass": "Standard"}
                            }
                        }
                    ],
                    "edges": [
                        {"source": "ec2-1", "target": "s3-1"}
                    ]
                }
            }
        }

class ArchitectureImprovementsRequest(BaseModel):
    """Request model for architecture improvement suggestions"""
    architecture_data: Dict[str, Any] = Field(..., description="Architecture data to analyze")
    focus_area: str = Field("general", description="Area to focus improvements on")
    
    class Config:
        schema_extra = {
            "example": {
                "architecture_data": {
                    "nodes": [{"id": "ec2-1", "data": {"service": {"name": "Amazon EC2"}}}],
                    "edges": []
                },
                "focus_area": "cost"
            }
        }

class ConversationHistoryRequest(BaseModel):
    """Request model for conversation history"""
    conversation_id: str = Field(..., description="Conversation identifier")

# Response Models
class ArchitectureChatResponse(BaseModel):
    """Response model for architecture chat"""
    status: str
    response: str
    conversation_id: str
    timestamp: str
    tools_used: List[str] = []
    context_provided: bool = False
    error: Optional[str] = None

class ArchitectureInsightsResponse(BaseModel):
    """Response model for architecture insights"""
    status: str
    insights: str
    timestamp: str
    error: Optional[str] = None

class ConversationHistoryResponse(BaseModel):
    """Response model for conversation history"""
    status: str
    history: List[Dict[str, Any]]
    conversation_id: str
    error: Optional[str] = None

# API Endpoints
@router.post("/chat", response_model=ArchitectureChatResponse, status_code=status.HTTP_200_OK)
async def chat_with_architecture(
    request: ArchitectureChatRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Chat with AI about architecture
    
    This endpoint allows users to ask questions about their cloud architecture,
    get optimization suggestions, cost analysis, and general cloud architecture advice.
    
    Features:
    - Architecture-aware responses using current architecture context
    - Web search integration for up-to-date information
    - Cost calculation and optimization suggestions
    - Conversation history tracking
    """
    try:
        result = await architecture_chat_service.chat_with_architecture(
            user_query=request.user_query,
            architecture_data=request.architecture_data,
            conversation_id=request.conversation_id
        )
        
        if result['status'] == 'error':
            raise HTTPException(
                status_code=400,
                detail=f"Chat processing failed: {result.get('error', 'Unknown error')}"
            )
        
        return ArchitectureChatResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/insights", response_model=ArchitectureInsightsResponse, status_code=status.HTTP_200_OK)
async def get_architecture_insights(
    request: ArchitectureInsightsRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Get comprehensive insights about an architecture
    
    Provides detailed analysis including:
    - Architecture pattern identification
    - Cost analysis and optimization opportunities
    - Security considerations
    - Scalability assessment
    - Best practices recommendations
    """
    try:
        result = await architecture_chat_service.get_architecture_insights(
            architecture_data=request.architecture_data
        )
        
        if result['status'] == 'error':
            raise HTTPException(
                status_code=400,
                detail=f"Insights generation failed: {result.get('error', 'Unknown error')}"
            )
        
        return ArchitectureInsightsResponse(
            status=result['status'],
            insights=result['response'],
            timestamp=result['timestamp'],
            error=result.get('error')
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/improvements", response_model=ArchitectureChatResponse, status_code=status.HTTP_200_OK)
async def suggest_architecture_improvements(
    request: ArchitectureImprovementsRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Suggest specific improvements for the architecture
    
    Provides actionable recommendations focusing on specified areas:
    - cost: Cost optimization suggestions
    - performance: Performance improvement recommendations
    - security: Security enhancement suggestions
    - scalability: Scalability improvement ideas
    - general: Overall architecture improvements
    """
    try:
        result = await architecture_chat_service.suggest_architecture_improvements(
            architecture_data=request.architecture_data,
            focus_area=request.focus_area
        )
        
        if result['status'] == 'error':
            raise HTTPException(
                status_code=400,
                detail=f"Improvement suggestions failed: {result.get('error', 'Unknown error')}"
            )
        
        return ArchitectureChatResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/history/{conversation_id}", response_model=ConversationHistoryResponse, status_code=status.HTTP_200_OK)
async def get_conversation_history(
    conversation_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get conversation history for a specific conversation
    
    Returns the complete conversation history including user messages
    and AI responses for the specified conversation ID.
    """
    try:
        history = architecture_chat_service.get_conversation_history(conversation_id)
        
        return ConversationHistoryResponse(
            status="success",
            history=history,
            conversation_id=conversation_id
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.delete("/history", status_code=status.HTTP_200_OK)
async def clear_conversation_history(
    current_user: User = Depends(get_current_user)
):
    """
    Clear conversation history
    
    Clears all conversation history for the current session.
    """
    try:
        success = architecture_chat_service.clear_conversation_history()
        
        if not success:
            raise HTTPException(
                status_code=400,
                detail="Failed to clear conversation history"
            )
        
        return {"status": "success", "message": "Conversation history cleared"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/health", status_code=status.HTTP_200_OK)
async def health_check():
    """
    Health check endpoint for architecture chat service
    """
    try:
        # Test if the service is properly initialized
        if architecture_chat_service.llm is None:
            raise HTTPException(
                status_code=503,
                detail="Architecture chat service not properly initialized"
            )
        
        return {
            "status": "healthy",
            "service": "architecture_chat",
            "tools_available": len(architecture_chat_service.tools),
            "llm_initialized": architecture_chat_service.llm is not None,
            "agent_initialized": architecture_chat_service.agent is not None
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"Service unhealthy: {str(e)}"
        )

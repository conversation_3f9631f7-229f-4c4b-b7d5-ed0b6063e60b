# FastAPI and web framework dependencies (Updated for Pydantic 2.x compatibility)
fastapi==0.115.0
uvicorn[standard]==0.30.0
python-multipart==0.0.9

# Machine Learning and Data Science
numpy==1.26.1
pandas==2.2.2
scikit-learn==1.5.1
joblib==1.4.2
xgboost==2.0.3

# RAG and NLP dependencies (Updated for compatibility)
langchain==0.2.16
langchain-community==0.2.16
langchain-core==0.2.38
langchain-text-splitters==0.2.4
langchain-openai==0.1.23
sentence-transformers==2.2.2
huggingface-hub==0.19.4
transformers==4.39.3
chromadb==0.4.24

# Web search dependencies
duckduckgo-search==5.3.0

# Azure OpenAI (updated for GPT-4o vision support)
openai==1.12.0

# Document processing
PyPDF2==3.0.1
python-docx==1.1.0
docx2txt==0.8

# Vector databases
pinecone-client==2.2.4

# Database dependencies
sqlalchemy==2.0.23
alembic==1.13.1

# Authentication dependencies
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.0.1
python-multipart==0.0.9

# Environment and configuration (Updated for compatibility)
python-dotenv==1.0.1
pydantic==2.7.4
pydantic-settings==2.10.1

# File handling
aiofiles==23.2.1

# Image processing (for Load Image functionality)
Pillow==10.2.0

# Utilities
requests==2.31.0
python-dateutil==2.8.2

# Development and testing (optional)
pytest==8.0.0
pytest-asyncio==0.23.6
httpx==0.25.0

# CORS support
flask-cors==4.0.0



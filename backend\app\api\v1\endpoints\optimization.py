"""
Architecture Optimization API endpoints

This module provides REST API endpoints for the architecture optimization feature,
replicating the PyQt5 MapleGUI "Optimize Architecture" functionality.
"""

from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from app.auth.dependencies import get_current_user
from app.database.models import User
from app.services.optimization_service import optimization_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


# Request Models
class OptimizationRequest(BaseModel):
    """Request model for architecture optimization"""
    target_service: str = Field(..., description="Target service to optimize (e.g., 'Amazon EC2', 'AWS Lambda')")
    throughput: str = Field(..., description="Required throughput (e.g., '1000')")
    latency_requirement: str = Field(..., description="Maximum acceptable latency in seconds (e.g., '10')")
    memory_requirement: str = Field(..., description="Required memory in MB (e.g., '1024')")
    current_memory: str = Field(..., description="Current memory configuration in MB (e.g., '512')")
    current_cores: str = Field(..., description="Current CPU cores (e.g., '2')")
    unit: str = Field(default="second", description="Time unit for throughput (e.g., 'second', 'hour')")
    nodes: List[Dict[str, Any]] = Field(default=[], description="Architecture nodes")
    edges: List[Dict[str, Any]] = Field(default=[], description="Architecture edges")


class ServiceAlternativesRequest(BaseModel):
    """Request model for getting service alternatives"""
    target_service: str = Field(..., description="Target service to find alternatives for")


# Response Models
class OptimizationRecommendation(BaseModel):
    """Optimization recommendation details"""
    action: str = Field(..., description="Recommended action: 'keep_current', 'optimize_configuration', 'replace_service'")
    current_service: str = Field(..., description="Current service name")
    recommended_service: str = Field(..., description="Recommended service name")
    recommendation_text: str = Field(..., description="AI-generated recommendation text")
    change_type: str = Field(..., description="Type of change: 'none', 'configuration', 'service_replacement'")
    justification: str = Field(..., description="Justification for the recommendation")


class ServicePerformanceAnalysis(BaseModel):
    """Service performance analysis results"""
    service: str
    current: Dict[str, Any]
    optimized: Dict[str, Any]


class OptimizationResponse(BaseModel):
    """Response model for optimization results"""
    status: str = Field(..., description="Status: 'success' or 'error'")
    target_service: str = Field(..., description="Target service that was optimized")
    current_configuration: Dict[str, str] = Field(..., description="Current service configuration")
    alternatives_analyzed: int = Field(..., description="Number of alternatives analyzed")
    recommendation: OptimizationRecommendation = Field(..., description="Optimization recommendation")
    optimization_results: Optional[Dict[str, Any]] = Field(None, description="Detailed optimization analysis results")
    error: Optional[str] = Field(None, description="Error message if optimization failed")


class ServiceAlternativesResponse(BaseModel):
    """Response model for service alternatives"""
    status: str = Field(..., description="Status: 'success' or 'error'")
    target_service: str = Field(..., description="Target service")
    alternatives: List[str] = Field(..., description="List of alternative services")
    error: Optional[str] = Field(None, description="Error message if request failed")


# API Endpoints
@router.post("/optimize", response_model=OptimizationResponse, status_code=status.HTTP_200_OK)
async def optimize_architecture(
    request: OptimizationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Optimize architecture for a specific service
    
    This endpoint replicates the PyQt5 MapleGUI "Optimize Architecture" functionality.
    It analyzes the current architecture and provides AI-powered recommendations for
    improving cost and performance.
    
    Process:
    1. Extract services from current architecture
    2. Generate alternative services using AI
    3. Analyze each alternative with ML predictions and RAG
    4. Generate final recommendation using AI decision engine
    
    Args:
        request: Optimization parameters including target service and requirements
        current_user: Authenticated user
        
    Returns:
        OptimizationResponse with recommendations and analysis results
    """
    try:
        logger.info(f"Starting architecture optimization for user {current_user.id}, target service: {request.target_service}")
        logger.info(f"Received {len(request.nodes)} nodes and {len(request.edges)} edges")

        # Debug: Log the first few nodes to see their structure
        for i, node in enumerate(request.nodes[:3]):  # Log first 3 nodes
            logger.info(f"Node {i}: {node}")

        # Convert request to optimization parameters
        optimization_params = {
            'target_service': request.target_service,
            'throughput': request.throughput,
            'latency_requirement': request.latency_requirement,
            'memory_requirement': request.memory_requirement,
            'current_memory': request.current_memory,
            'current_cores': request.current_cores,
            'unit': request.unit,
            'nodes': request.nodes,
            'edges': request.edges
        }
        
        # Perform optimization analysis
        result = await optimization_service.optimize_architecture(optimization_params)
        
        if result['status'] == 'error':
            raise HTTPException(
                status_code=400,
                detail=f"Optimization failed: {result.get('error', 'Unknown error')}"
            )
        
        # Convert result to response format
        response = OptimizationResponse(
            status=result['status'],
            target_service=result['target_service'],
            current_configuration=result['current_configuration'],
            alternatives_analyzed=result['alternatives_analyzed'],
            recommendation=OptimizationRecommendation(**result['recommendation']),
            optimization_results=result.get('optimization_results')
        )
        
        logger.info(f"Optimization completed successfully for user {current_user.id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in architecture optimization: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during optimization: {str(e)}"
        )


@router.post("/alternatives", response_model=ServiceAlternativesResponse, status_code=status.HTTP_200_OK)
async def get_service_alternatives(
    request: ServiceAlternativesRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Get alternative services for a given target service
    
    This endpoint provides a list of alternative AWS services that could potentially
    replace the target service, using AI-powered service analysis.
    
    Args:
        request: Service alternatives request with target service
        current_user: Authenticated user
        
    Returns:
        ServiceAlternativesResponse with list of alternative services
    """
    try:
        logger.info(f"Getting alternatives for service: {request.target_service}")
        
        # Generate alternatives using the optimization service
        alternatives = await optimization_service._generate_service_alternatives(request.target_service)
        
        response = ServiceAlternativesResponse(
            status='success',
            target_service=request.target_service,
            alternatives=alternatives
        )
        
        logger.info(f"Found {len(alternatives)} alternatives for {request.target_service}")
        return response
        
    except Exception as e:
        logger.error(f"Error getting service alternatives: {str(e)}")
        return ServiceAlternativesResponse(
            status='error',
            target_service=request.target_service,
            alternatives=[],
            error=str(e)
        )


@router.post("/debug-nodes", status_code=status.HTTP_200_OK)
async def debug_architecture_nodes(
    request: OptimizationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Debug endpoint to check how nodes are being received and processed
    """
    try:
        logger.info(f"Debug: Received {len(request.nodes)} nodes")

        extracted_services = []
        for i, node in enumerate(request.nodes):
            logger.info(f"Debug Node {i}: {node}")

            service_data = node.get('data', {})
            service_name = ''

            if service_data.get('service', {}).get('name'):
                service_name = service_data['service']['name']
            elif service_data.get('label'):
                service_name = service_data['label']
            elif service_data.get('name'):
                service_name = service_data['name']

            if service_name and 'User' not in service_name:
                extracted_services.append(service_name)

        return {
            'status': 'success',
            'nodes_received': len(request.nodes),
            'extracted_services': extracted_services,
            'target_service': request.target_service,
            'raw_nodes': request.nodes[:2]  # First 2 nodes for inspection
        }

    except Exception as e:
        logger.error(f"Debug endpoint error: {str(e)}")
        return {
            'status': 'error',
            'error': str(e)
        }


@router.get("/services", status_code=status.HTTP_200_OK)
async def get_optimizable_services(current_user: User = Depends(get_current_user)):
    """
    Get list of services that can be optimized
    
    Returns a list of AWS services that are supported by the optimization engine.
    
    Args:
        current_user: Authenticated user
        
    Returns:
        List of optimizable services with their categories
    """
    try:
        optimizable_services = {
            'compute': [
                'Amazon EC2',
                'AWS Lambda',
                'Amazon ECS',
                'AWS Fargate',
                'Amazon Lightsail',
                'AWS Batch'
            ],
            'storage': [
                'Amazon S3',
                'Amazon S3 Glacier',
                'Amazon EFS',
                'Amazon EBS',
                'Amazon FSx',
                'AWS Storage Gateway'
            ],
            'database': [
                'Amazon DynamoDB',
                'Amazon DynamoDB Accelerator (DAX)',
                'Amazon RDS',
                'Amazon Aurora',
                'Amazon DocumentDB',
                'Amazon Neptune',
                'Amazon Timestream',
                'Amazon QLDB'
            ],
            'cache': [
                'Amazon ElastiCache',
                'Amazon MemoryDB'
            ],
            'analytics': [
                'Amazon Athena',
                'Amazon QuickSight',
                'Amazon Redshift',
                'Amazon EMR',
                'AWS Glue',
                'Amazon Kinesis Data Analytics'
            ],
            'networking': [
                'Amazon VPC',
                'Amazon CloudFront',
                'AWS Global Accelerator',
                'Amazon Route 53',
                'AWS Direct Connect',
                'Amazon API Gateway'
            ],
            'security': [
                'AWS WAF',
                'AWS Shield',
                'Amazon Cognito',
                'AWS IAM',
                'AWS KMS',
                'AWS Secrets Manager'
            ],
            'monitoring': [
                'Amazon CloudWatch',
                'AWS X-Ray',
                'AWS CloudTrail',
                'Amazon Inspector'
            ],
            'messaging': [
                'Amazon SQS',
                'Amazon SNS',
                'Amazon MQ',
                'Amazon Kinesis',
                'Amazon EventBridge'
            ],
            'ml_ai': [
                'Amazon SageMaker',
                'Amazon Rekognition',
                'Amazon Comprehend',
                'Amazon Textract',
                'Amazon Translate',
                'Amazon Polly'
            ],
            'iot': [
                'AWS IoT Core',
                'AWS IoT Analytics',
                'AWS IoT Device Management',
                'AWS IoT Greengrass'
            ]
        }
        
        return {
            'status': 'success',
            'services': optimizable_services,
            'total_services': sum(len(services) for services in optimizable_services.values())
        }
        
    except Exception as e:
        logger.error(f"Error getting optimizable services: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving optimizable services: {str(e)}"
        )

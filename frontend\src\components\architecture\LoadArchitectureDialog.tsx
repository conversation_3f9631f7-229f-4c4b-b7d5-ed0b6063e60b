import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { architectureApi } from '@/services/api'
import {
  Search,
  Calendar,
  DollarSign,
  Clock,
  Layers,
  Network,
  Trash2,
  Download,
  Eye,
  AlertCircle
} from 'lucide-react'

interface SavedArchitecture {
  id: number
  name: string
  description: string
  architecture_type: string
  total_cost: number
  total_latency: number
  version: number
  created_at: string
  updated_at: string
  node_count: number
  edge_count: number
}

interface LoadArchitectureDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onArchitectureLoaded: (architectureData: any) => void
}

export default function LoadArchitectureDialog({
  open,
  onOpenChange,
  onArchitectureLoaded
}: LoadArchitectureDialogProps) {
  const [architectures, setArchitectures] = useState<SavedArchitecture[]>([])
  const [filteredArchitectures, setFilteredArchitectures] = useState<SavedArchitecture[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedArchitecture, setSelectedArchitecture] = useState<SavedArchitecture | null>(null)
  const { toast } = useToast()

  // Load architectures when dialog opens
  useEffect(() => {
    if (open) {
      loadArchitectures()
    }
  }, [open])

  // Filter architectures based on search term
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredArchitectures(architectures)
    } else {
      const filtered = architectures.filter(arch =>
        arch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        arch.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        arch.architecture_type.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredArchitectures(filtered)
    }
  }, [searchTerm, architectures])

  const loadArchitectures = async () => {
    setLoading(true)
    try {
      const response = await architectureApi.listUserArchitectures()
      if (response.success) {
        setArchitectures(response.architectures)
        setFilteredArchitectures(response.architectures)
      } else {
        throw new Error('Failed to load architectures')
      }
    } catch (error) {
      console.error('Error loading architectures:', error)
      toast({
        title: "Load Error",
        description: "Failed to load saved architectures.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleLoadArchitecture = async (architecture: SavedArchitecture) => {
    setLoading(true)
    try {
      const response = await architectureApi.loadFromDatabase(architecture.id)
      if (response.success) {
        // Convert the loaded architecture data to the expected format
        const architectureData = {
          name: response.architecture.name,
          nodes: response.architecture.nodes,
          edges: response.architecture.edges,
          metadata: response.architecture.metadata
        }
        
        onArchitectureLoaded(architectureData)
        onOpenChange(false)
        
        toast({
          title: "Architecture Loaded",
          description: `"${architecture.name}" has been loaded successfully.`,
        })
      } else {
        throw new Error('Failed to load architecture')
      }
    } catch (error) {
      console.error('Error loading architecture:', error)
      toast({
        title: "Load Error",
        description: `Failed to load "${architecture.name}".`,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteArchitecture = async (architecture: SavedArchitecture) => {
    if (!confirm(`Are you sure you want to delete "${architecture.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await architectureApi.deleteFromDatabase(architecture.id)
      if (response.success) {
        // Remove from local state
        const updatedArchitectures = architectures.filter(arch => arch.id !== architecture.id)
        setArchitectures(updatedArchitectures)
        setFilteredArchitectures(updatedArchitectures.filter(arch =>
          searchTerm.trim() === '' ||
          arch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          arch.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          arch.architecture_type.toLowerCase().includes(searchTerm.toLowerCase())
        ))
        
        toast({
          title: "Architecture Deleted",
          description: response.message,
        })
      } else {
        throw new Error('Failed to delete architecture')
      }
    } catch (error) {
      console.error('Error deleting architecture:', error)
      toast({
        title: "Delete Error",
        description: `Failed to delete "${architecture.name}".`,
        variant: "destructive"
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCost = (cost: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 6,
      maximumFractionDigits: 6
    }).format(cost)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Load Architecture from Cloud
          </DialogTitle>
          <DialogDescription>
            Select a saved architecture to load into the designer
          </DialogDescription>
        </DialogHeader>

        {/* Search Bar */}
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search architectures by name, description, or type..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button onClick={loadArchitectures} variant="outline" disabled={loading}>
            Refresh
          </Button>
        </div>

        {/* Architecture List */}
        <ScrollArea className="h-[400px] w-full">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-muted-foreground">Loading architectures...</div>
            </div>
          ) : filteredArchitectures.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 space-y-2">
              <AlertCircle className="h-8 w-8 text-muted-foreground" />
              <div className="text-muted-foreground">
                {architectures.length === 0 ? 'No saved architectures found' : 'No architectures match your search'}
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredArchitectures.map((architecture) => (
                <Card 
                  key={architecture.id} 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedArchitecture?.id === architecture.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setSelectedArchitecture(architecture)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <CardTitle className="text-lg">{architecture.name}</CardTitle>
                        <CardDescription className="text-sm">
                          {architecture.description || 'No description provided'}
                        </CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">{architecture.architecture_type}</Badge>
                        <Badge variant="outline">v{architecture.version}</Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Layers className="h-4 w-4 text-muted-foreground" />
                        <span>{architecture.node_count} services</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Network className="h-4 w-4 text-muted-foreground" />
                        <span>{architecture.edge_count} connections</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span>{formatCost(architecture.total_cost)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{architecture.total_latency.toFixed(2)}s</span>
                      </div>
                    </div>
                    <Separator className="my-3" />
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        <span>Created: {formatDate(architecture.created_at)}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleLoadArchitecture(architecture)
                          }}
                          disabled={loading}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Load
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteArchitecture(architecture)
                          }}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            {filteredArchitectures.length} architecture{filteredArchitectures.length !== 1 ? 's' : ''} found
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            {selectedArchitecture && (
              <Button 
                onClick={() => handleLoadArchitecture(selectedArchitecture)}
                disabled={loading}
              >
                Load Selected Architecture
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

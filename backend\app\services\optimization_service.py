"""
Architecture Optimization Service

This service replicates the PyQt5 MapleGUI "Optimize Architecture" functionality,
providing AI-powered architecture optimization with ML-based cost and latency predictions.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from openai import AsyncAzureOpenAI

from app.core.config import settings
from app.services.ml_service import ml_service
from app.services.rag_service import rag_service

logger = logging.getLogger(__name__)


class OptimizationService:
    """
    Service for optimizing cloud architectures using AI and ML predictions
    
    Replicates the exact functionality from PyQt5 MapleGUI optimizeArchitecture() method
    """
    
    def __init__(self):
        """Initialize the optimization service"""
        self.azure_client = None
        if settings.AZURE_OPENAI_API_KEY and settings.AZURE_OPENAI_ENDPOINT:
            self.azure_client = AsyncAzureOpenAI(
                api_key=settings.AZURE_OPENAI_API_KEY,
                api_version=settings.AZURE_OPENAI_API_VERSION,
                azure_endpoint=settings.AZURE_OPENAI_ENDPOINT
            )
    
    async def optimize_architecture(self, optimization_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main optimization function that replicates PyQt5 optimizeArchitecture()
        
        Args:
            optimization_request: Contains target service, workload requirements, and current architecture
            
        Returns:
            Dictionary containing optimization results and recommendations
        """
        try:
            # Extract optimization parameters
            target_service = optimization_request.get('target_service')
            throughput = optimization_request.get('throughput', '1000')
            latency_requirement = optimization_request.get('latency_requirement', '10')
            memory_requirement = optimization_request.get('memory_requirement', '1024')
            current_memory = optimization_request.get('current_memory', '1024')
            current_cores = optimization_request.get('current_cores', '2')
            unit = optimization_request.get('unit', 'second')
            architecture_nodes = optimization_request.get('nodes', [])
            architecture_edges = optimization_request.get('edges', [])
            
            logger.info(f"Starting optimization for target service: {target_service}")
            
            # Step 1: Extract services from architecture (replicates PyQt5 adjacency_matrix logic)
            services = self._extract_services_from_architecture(architecture_nodes, architecture_edges)
            
            # Step 2: Find target service in architecture
            target_services = [service for service in services if target_service in service.get('name', '')]
            
            if not target_services:
                return {
                    'status': 'error',
                    'error': f'Target service {target_service} not found in architecture'
                }
            
            # Step 3: Generate alternative services using AI
            alternatives = await self._generate_service_alternatives(target_service)
            
            # Step 4: Analyze each alternative with RAG and ML predictions
            optimization_results = await self._analyze_service_alternatives(
                target_service=target_service,
                alternatives=alternatives,
                throughput=throughput,
                latency_requirement=latency_requirement,
                memory_requirement=memory_requirement,
                current_memory=current_memory,
                current_cores=current_cores,
                unit=unit
            )
            
            # Step 5: Generate final recommendation using AI
            recommendation = await self._generate_optimization_recommendation(
                target_service=target_service,
                optimization_results=optimization_results,
                throughput=throughput,
                latency_requirement=latency_requirement,
                memory_requirement=memory_requirement,
                current_memory=current_memory,
                current_cores=current_cores
            )
            
            return {
                'status': 'success',
                'target_service': target_service,
                'current_configuration': {
                    'memory': current_memory,
                    'cores': current_cores
                },
                'alternatives_analyzed': len(alternatives),
                'recommendation': recommendation,
                'optimization_results': optimization_results
            }
            
        except Exception as e:
            logger.error(f"Error in architecture optimization: {str(e)}")
            return {
                'status': 'error',
                'error': f'Optimization failed: {str(e)}'
            }
    
    def _extract_services_from_architecture(self, nodes: List[Dict], edges: List[Dict]) -> List[Dict]:
        """
        Extract services from architecture nodes and edges (replicates PyQt5 adjacency_matrix logic)
        """
        services = []
        
        # Extract services from nodes
        for node in nodes:
            service_data = node.get('data', {})
            service_name = service_data.get('service', {}).get('name', '') or service_data.get('label', '')
            
            if service_name and 'User' not in service_name:
                services.append({
                    'id': node.get('id'),
                    'name': service_name,
                    'type': service_data.get('service', {}).get('type', ''),
                    'config': service_data.get('config', {})
                })
        
        return services
    
    async def _generate_service_alternatives(self, target_service: str) -> List[str]:
        """
        Generate alternative services using Azure OpenAI (replicates PyQt5 getresponse logic)
        """
        if not self.azure_client:
            # Fallback alternatives based on service type
            return self._get_fallback_alternatives(target_service)
        
        try:
            prompt = (
                f"List all AWS services that are possible replacements for the service {target_service}. "
                f"The services you list should be similar to {target_service} according to their functionality. "
                f"Only output the AWS services names one by one separated by newlines. "
                f"Do not write any more text. Do not provide any explanation. "
                f"Do not perform numbering or bulleting of the output."
            )
            
            response = await self.azure_client.chat.completions.create(
                messages=[{"role": "user", "content": prompt}],
                model=settings.AZURE_OPENAI_DEPLOYMENT_NAME,
                temperature=0.7,
                max_tokens=100
            )
            
            alternatives = response.choices[0].message.content.strip().split('\n')
            alternatives = [alt.strip() for alt in alternatives if alt.strip()]
            
            # Add the original service and apply service-specific rules (replicates PyQt5 logic)
            final_alternatives = [target_service] + alternatives
            
            # Apply PyQt5 service-specific rules
            if "EC2" in target_service and "AWS Lambda" not in final_alternatives:
                final_alternatives.append("AWS Lambda")
            if "S3" in target_service and "Glacier" not in final_alternatives:
                final_alternatives.append("Amazon S3 Glacier")
            if "DynamoDB" in target_service:
                final_alternatives = ["Amazon DynamoDB Accelerator (DAX)"]
            
            return final_alternatives
            
        except Exception as e:
            logger.error(f"Error generating alternatives: {str(e)}")
            return self._get_fallback_alternatives(target_service)
    
    def _get_fallback_alternatives(self, target_service: str) -> List[str]:
        """
        Provide fallback alternatives when AI is not available
        """
        fallback_map = {
            'Amazon EC2': ['Amazon EC2', 'AWS Lambda', 'Amazon ECS'],
            'AWS Lambda': ['AWS Lambda', 'Amazon EC2', 'AWS Fargate'],
            'Amazon S3': ['Amazon S3', 'Amazon S3 Glacier', 'Amazon EFS'],
            'Amazon DynamoDB': ['Amazon DynamoDB', 'Amazon DynamoDB Accelerator (DAX)', 'Amazon RDS'],
            'Amazon RDS': ['Amazon RDS', 'Amazon DynamoDB', 'Amazon Aurora'],
            'Amazon ElastiCache': ['Amazon ElastiCache', 'Amazon DynamoDB', 'Amazon MemoryDB']
        }
        
        return fallback_map.get(target_service, [target_service])

    def _get_elasticache_instance_type(self, memory_mb: int, cores: int) -> str:
        """
        Map memory and cores to ElastiCache instance type (replicates PyQt5 logic)
        """
        try:
            memory_mb = int(memory_mb)
            cores = int(cores)

            # ElastiCache instance type mapping (from PyQt5)
            if memory_mb <= 1024 and cores == 1:
                return 't2.micro'
            elif memory_mb <= 2048 and cores == 1:
                return 't2.small'
            elif memory_mb <= 1024 and cores == 2:
                return 't3.micro'
            elif memory_mb <= 2048 and cores == 2:
                return 't3.small'
            else:
                # Default to t3.micro for unknown configurations
                return 't3.micro'

        except (ValueError, TypeError):
            return 't3.micro'

    async def _analyze_service_alternatives(self, target_service: str, alternatives: List[str],
                                          throughput: str, latency_requirement: str, memory_requirement: str,
                                          current_memory: str, current_cores: str, unit: str) -> Dict[str, Any]:
        """
        Analyze each alternative service using ML predictions and RAG (replicates PyQt5 analysis logic)
        """
        analysis_results = {}

        for service in alternatives:
            if 'User' in service:
                continue

            # Get ML predictions for the service
            service_analysis = await self._get_service_performance_analysis(
                service=service,
                throughput=throughput,
                memory_requirement=memory_requirement,
                current_memory=current_memory,
                current_cores=current_cores
            )

            # Get RAG-based documentation analysis
            rag_analysis = await self._get_rag_service_analysis(
                service=service,
                throughput=throughput,
                latency_requirement=latency_requirement,
                memory_requirement=memory_requirement,
                current_memory=current_memory,
                current_cores=current_cores,
                unit=unit
            )

            analysis_results[service] = {
                'ml_analysis': service_analysis,
                'rag_analysis': rag_analysis
            }

        return analysis_results

    async def _get_service_performance_analysis(self, service: str, throughput: str,
                                              memory_requirement: str, current_memory: str,
                                              current_cores: str) -> Dict[str, Any]:
        """
        Get ML-based performance analysis for a service (replicates PyQt5 ML function calls)
        """
        try:
            workload = int(throughput)
            memory_mb = int(memory_requirement)
            current_mem_mb = int(current_memory)
            cores = int(current_cores)

            # Use ML service to get predictions based on service type
            if "Lambda" in service:
                # Lambda optimization
                current_result = ml_service.lambda_prediction.compute_lambda_latency_and_cost(
                    workload_invocations=workload,
                    memory_mb=current_mem_mb,
                    function_defn="DTS_deepreader",
                    memory_required=memory_mb
                )

                new_result = ml_service.lambda_prediction.compute_lambda_latency_and_cost(
                    workload_invocations=workload,
                    memory_mb=memory_mb,
                    function_defn="DTS_deepreader",
                    memory_required=memory_mb
                )

                return {
                    'service': service,
                    'current': {
                        'latency': current_result['latency'],
                        'cost': current_result['cost'],
                        'memory': current_mem_mb
                    },
                    'optimized': {
                        'latency': new_result['latency'],
                        'cost': new_result['cost'],
                        'memory': memory_mb
                    }
                }

            elif "S3" in service:
                # S3 optimization
                current_result = ml_service.s3_prediction.compute_s3_latency_and_cost(
                    workload=workload,
                    file_size=20480,  # Default file size from PyQt5
                    memory=current_mem_mb,
                    operation="read"
                )

                new_result = ml_service.s3_prediction.compute_s3_latency_and_cost(
                    workload=workload,
                    file_size=20480,
                    memory=memory_mb,
                    operation="read"
                )

                return {
                    'service': service,
                    'current': {
                        'latency': current_result['latency'],
                        'cost': current_result['cost'],
                        'memory': current_mem_mb
                    },
                    'optimized': {
                        'latency': new_result['latency'],
                        'cost': new_result['cost'],
                        'memory': memory_mb
                    }
                }

            elif "DynamoDB" in service:
                # DynamoDB optimization
                current_result = ml_service.dynamodb_prediction.compute_dynamodb_latency_and_cost(
                    workload=workload,
                    data_size=100000,  # Default from PyQt5
                    chunk_size=10000
                )

                # For DAX, apply cost multiplier and latency improvement
                if "DAX" in service:
                    return {
                        'service': service,
                        'current': {
                            'latency': current_result['latency'],
                            'cost': current_result['cost'],
                            'memory': current_mem_mb
                        },
                        'optimized': {
                            'latency': 0.01,  # DAX latency from PyQt5
                            'cost': current_result['cost'] * 2,  # DAX cost multiplier
                            'memory': memory_mb
                        }
                    }

                return {
                    'service': service,
                    'current': {
                        'latency': current_result['latency'],
                        'cost': current_result['cost'],
                        'memory': current_mem_mb
                    },
                    'optimized': {
                        'latency': current_result['latency'],
                        'cost': current_result['cost'],
                        'memory': memory_mb
                    }
                }

            elif "ElastiCache" in service:
                # ElastiCache optimization
                # Map memory and cores to instance type
                current_instance_type = self._get_elasticache_instance_type(current_mem_mb, cores)
                new_instance_type = self._get_elasticache_instance_type(memory_mb, cores)

                current_result = ml_service.predict_elasticache_cost(
                    workload=workload,
                    instance_type=current_instance_type
                )

                new_result = ml_service.predict_elasticache_cost(
                    workload=workload,
                    instance_type=new_instance_type
                )

                return {
                    'service': service,
                    'current': {
                        'latency': current_result['latency'],
                        'cost': current_result['cost'],
                        'memory': current_mem_mb,
                        'instance_type': current_instance_type
                    },
                    'optimized': {
                        'latency': new_result['latency'],
                        'cost': new_result['cost'],
                        'memory': memory_mb,
                        'instance_type': new_instance_type
                    }
                }

            elif "EC2" in service:
                # EC2 optimization (using PyQt5 formula)
                latency = (0.05625 * workload) - (0.05625 * cores) + 0.45
                cost = 1.76  # Base EC2 cost from PyQt5

                return {
                    'service': service,
                    'current': {
                        'latency': latency,
                        'cost': cost,
                        'memory': current_mem_mb
                    },
                    'optimized': {
                        'latency': latency,
                        'cost': cost,
                        'memory': memory_mb
                    }
                }

            else:
                # Generic service analysis
                return {
                    'service': service,
                    'current': {
                        'latency': 1.0,
                        'cost': 0.1,
                        'memory': current_mem_mb
                    },
                    'optimized': {
                        'latency': 1.0,
                        'cost': 0.1,
                        'memory': memory_mb
                    }
                }

        except Exception as e:
            logger.error(f"Error in service performance analysis for {service}: {str(e)}")
            return {
                'service': service,
                'error': str(e)
            }

    async def _get_rag_service_analysis(self, service: str, throughput: str, latency_requirement: str,
                                      memory_requirement: str, current_memory: str, current_cores: str,
                                      unit: str) -> Dict[str, Any]:
        """
        Get RAG-based analysis from AWS documentation (replicates PyQt5 RAG retrieval logic)
        """
        try:
            # Construct query similar to PyQt5
            query = (
                f"I have workload requirement, that I need latency of {latency_requirement} seconds "
                f"and throughput of {throughput} requests per {unit} and memory of {memory_requirement} MB. "
                f"Is {service} with memory configuration {current_memory} and cores {current_cores}, "
                f"sufficient for that workload? If no what other configuration of {service} is needed "
                f"or state which other alternative service can be used instead of {service} for this?"
            )

            # Use RAG service to get relevant documentation
            rag_result = rag_service.query_documents(query, top_k=10)

            if rag_result['status'] == 'success':
                return {
                    'service': service,
                    'query': query,
                    'documents': rag_result.get('context', []),
                    'sources': rag_result.get('sources', [])
                }
            else:
                return {
                    'service': service,
                    'query': query,
                    'documents': [],
                    'sources': [],
                    'error': rag_result.get('error', 'RAG query failed')
                }

        except Exception as e:
            logger.error(f"Error in RAG analysis for {service}: {str(e)}")
            return {
                'service': service,
                'error': str(e)
            }

    async def _generate_optimization_recommendation(self, target_service: str, optimization_results: Dict[str, Any],
                                                  throughput: str, latency_requirement: str, memory_requirement: str,
                                                  current_memory: str, current_cores: str) -> Dict[str, Any]:
        """
        Generate final optimization recommendation using AI (replicates PyQt5 recommendation logic)
        """
        if not self.azure_client:
            return self._get_fallback_recommendation(target_service, optimization_results)

        try:
            # Prepare documents from all analyses
            all_documents = []
            for service, analysis in optimization_results.items():
                ml_data = analysis.get('ml_analysis', {})
                rag_data = analysis.get('rag_analysis', {})

                # Add ML analysis results as documents
                if 'current' in ml_data and 'optimized' in ml_data:
                    current = ml_data['current']
                    optimized = ml_data['optimized']

                    doc = (
                        f"The latency obtained for {service} of memory configuration {current['memory']} MB, "
                        f"is {current['latency']:.2f} seconds and cost per hour is {current['cost']:.8f} USD, "
                        f"for the workload of throughput of {throughput} requests per second, "
                        f"memory of {memory_requirement} MB and latency requirement of {latency_requirement} secs."
                    )
                    all_documents.append(doc)

                    if optimized['memory'] != current['memory']:
                        opt_doc = (
                            f"The latency obtained for {service} of memory configuration {optimized['memory']} MB, "
                            f"is {optimized['latency']:.2f} seconds and cost per hour is {optimized['cost']:.8f} USD, "
                            f"for the workload of throughput of {throughput} requests per second, "
                            f"memory of {memory_requirement} MB and latency requirement of {latency_requirement} secs."
                        )
                        all_documents.append(opt_doc)

                # Add RAG documents
                rag_docs = rag_data.get('documents', [])
                all_documents.extend(rag_docs)

            # Generate recommendation prompt (replicates PyQt5 prompt structure)
            prompt = self._build_optimization_prompt(
                target_service=target_service,
                throughput=throughput,
                latency_requirement=latency_requirement,
                memory_requirement=memory_requirement,
                current_memory=current_memory,
                current_cores=current_cores,
                documents=all_documents
            )

            # Get AI recommendation
            response = await self.azure_client.chat.completions.create(
                messages=[{"role": "user", "content": prompt}],
                model=settings.AZURE_OPENAI_DEPLOYMENT_NAME,
                temperature=0.7,
                max_tokens=100
            )

            recommendation_text = response.choices[0].message.content.strip()

            # Parse recommendation and determine action type
            recommendation = self._parse_recommendation(
                recommendation_text=recommendation_text,
                target_service=target_service,
                optimization_results=optimization_results,
                latency_requirement=float(latency_requirement)
            )

            return recommendation

        except Exception as e:
            logger.error(f"Error generating recommendation: {str(e)}")
            return self._get_fallback_recommendation(target_service, optimization_results)

    def _build_optimization_prompt(self, target_service: str, throughput: str, latency_requirement: str,
                                 memory_requirement: str, current_memory: str, current_cores: str,
                                 documents: List[str]) -> str:
        """
        Build optimization prompt (replicates PyQt5 prompt structure)
        """
        base_prompt = (
            f"I have a query. Please answer the query. I have provided some documents. "
            f"Please answer the query from the documents provided only. Do Not use your own knowledge to answer the query. "
            f"Only answer the query by using the information provided in the documents I have provided below.\n\n"
            f"Query: I have workload requirement, that I need latency of {latency_requirement} seconds "
            f"and throughput of {throughput} requests per second and memory of {memory_requirement} MB. "
            f"Is {target_service} sufficient for that workload? "
        )

        # Add service-specific configuration details
        if "ElastiCache" in target_service:
            base_prompt += (
                f"The memory configuration for {target_service} is {current_memory} MB, and have {current_cores} cores. "
                f"For this you need to check whether the workload parameters are within the service parameters. "
                f"If no what other configuration of {target_service} is needed or state which other alternative service can be used instead of {target_service} for this? "
                f"If the answer is that {target_service} is sufficient, output {target_service} itself, along with its configuration. "
                f"Otherwise just state the name of the alternate service along with its configuration. "
                f"Do not unnecessarily predict other services if the current is sufficient. "
                f"So if the latency of the service is less than the workload latency, predict {target_service} with its configuration. "
                f"This is first priority. Second priority is to check if a lower configuration of {target_service} is sufficient. "
                f"If yes then mention the new configuration. If lower configuration is sufficient, then never predict new service. "
                f"In this case just output the new configuration. If none of the services in the documents meet the latency criteria, "
                f"first try to check which service or configuration has lowest latency. If latency is also same, mention the service "
                f"or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. "
                f"Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. "
                f"Do not write any service that is not in the documents. But definitely provide the configuration of the service. "
                f"For configuration provide memory and cores. Do not miss out on the configuration. Do not forget to mention cores. "
                f"Exactly mention the memory and cores. Not just some vague configuration"
            )
        elif target_service in ["Amazon EC2", "AWS Lambda", "Amazon DynamoDB"]:
            base_prompt += (
                f"For this you need to check whether the workload parameters are within the service parameters. "
                f"If no what other configuration of {target_service} is needed or state which other alternative service can be used instead of {target_service} for this? "
                f"If the answer is that {target_service} is sufficient, output {target_service} itself, along with its configuration. "
                f"Otherwise just state the name of the alternate service along with its configuration. "
                f"Do not unnecessarily predict other services if the current is sufficient. "
                f"So if the latency of the service is less than the workload latency, predict {target_service} with its configuration. "
                f"This is first priority. Second priority is to check if a lower configuration of {target_service} is sufficient. "
                f"If yes then mention the new configuration. If lower configuration is sufficient, then never predict new service. "
                f"In this case just output the new configuration. If none of the services in the documents meet the latency criteria, "
                f"first try to check which service or configuration has lowest latency. If latency is also same, mention the service "
                f"or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. "
                f"Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. "
                f"Do not write any service that is not in the documents. But definitely provide the configuration of the service. "
                f"Do not miss out on the configuration."
            )
        elif "S3" in target_service:
            base_prompt += (
                f"For this you need to check whether the workload parameters are within the service parameters. "
                f"If no what other configuration of {target_service} is needed or state which other alternative service can be used instead of {target_service} for this? "
                f"If the answer is that {target_service} is sufficient, output {target_service} itself. "
                f"Otherwise just state the name of the alternate service along with its configuration. "
                f"If both {target_service} and the new service has latency less than the workload latency, always predict the service with lesser cost, never predict {target_service}. "
                f"This is first priority. Second priority is to check if a lower configuration of {target_service} is sufficient. "
                f"If yes then mention the new configuration. In this case just output the new configuration. "
                f"If none of the services in the documents meet the latency criteria, first try to check which service or configuration has lowest latency. "
                f"If latency is also same, mention the service or the configuration that has lowest cost per hour. "
                f"So in this case, do not mention the configuration that has the higher cost per hour. "
                f"Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. "
                f"Do not write any service that is not in the documents. But if you can from the documents, definitely provide the configuration if the new service."
            )
        else:
            base_prompt += (
                f"For this you need to check whether the workload parameters are within the service parameters. "
                f"If no what other configuration of {target_service} is needed or state which other alternative service can be used instead of {target_service} for this? "
                f"If the answer is that {target_service} is sufficient, output {target_service} itself, along with its configuration. "
                f"Otherwise just state the name of the alternate service along with its configuration. "
                f"Do not unnecessarily predict other services if the current is sufficient. "
                f"So if the latency of the service is less than the workload latency, predict {target_service} with its configuration. "
                f"This is first priority. Second priority is to check if a lower configuration of {target_service} is sufficient. "
                f"If yes then mention the new configuration. If lower configuration is sufficient, then never predict new service. "
                f"In this case just output the new configuration. If none of the services in the documents meet the latency criteria, "
                f"first try to check which service or configuration has lowest latency. If latency is also same, mention the service "
                f"or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. "
                f"Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. "
                f"Do not write any service that is not in the documents. But definitely provide the configuration of the service. "
                f"Do not miss out on the configuration."
            )

        # Add documents
        base_prompt += f"\n\nDocuments:\n" + "\n".join(documents)

        return base_prompt

    def _parse_recommendation(self, recommendation_text: str, target_service: str,
                            optimization_results: Dict[str, Any], latency_requirement: float) -> Dict[str, Any]:
        """
        Parse AI recommendation and determine action type (replicates PyQt5 msgbox logic)
        """
        try:
            # Determine if recommendation suggests keeping current service or switching
            if target_service in recommendation_text:
                # Check if it's a configuration change or service replacement
                if any(config_word in recommendation_text.lower() for config_word in ['memory', 'mb', 'cores', 'configuration']):
                    # Configuration optimization
                    return {
                        'action': 'optimize_configuration',
                        'current_service': target_service,
                        'recommended_service': target_service,
                        'recommendation_text': recommendation_text,
                        'change_type': 'configuration',
                        'justification': 'Configuration optimization recommended'
                    }
                else:
                    # Keep current service
                    return {
                        'action': 'keep_current',
                        'current_service': target_service,
                        'recommended_service': target_service,
                        'recommendation_text': recommendation_text,
                        'change_type': 'none',
                        'justification': 'Current service is sufficient'
                    }
            else:
                # Service replacement recommended
                recommended_service = self._extract_recommended_service(recommendation_text)
                return {
                    'action': 'replace_service',
                    'current_service': target_service,
                    'recommended_service': recommended_service,
                    'recommendation_text': recommendation_text,
                    'change_type': 'service_replacement',
                    'justification': 'Alternative service recommended for better performance/cost'
                }

        except Exception as e:
            logger.error(f"Error parsing recommendation: {str(e)}")
            return self._get_fallback_recommendation(target_service, optimization_results)

    def _extract_recommended_service(self, recommendation_text: str) -> str:
        """
        Extract recommended service name from AI response
        """
        # Common AWS service patterns
        aws_services = [
            'AWS Lambda', 'Amazon EC2', 'Amazon S3', 'Amazon DynamoDB',
            'Amazon DynamoDB Accelerator (DAX)', 'Amazon S3 Glacier',
            'Amazon ElastiCache', 'Amazon RDS', 'Amazon ECS', 'AWS Fargate'
        ]

        for service in aws_services:
            if service in recommendation_text:
                return service

        # If no specific service found, return the recommendation text
        return recommendation_text.strip()

    def _get_fallback_recommendation(self, target_service: str, optimization_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Provide fallback recommendation when AI analysis fails
        """
        return {
            'action': 'keep_current',
            'current_service': target_service,
            'recommended_service': target_service,
            'recommendation_text': f'Current {target_service} configuration is sufficient for the workload.',
            'change_type': 'none',
            'justification': 'Fallback recommendation - insufficient data for optimization'
        }


# Create global instance
optimization_service = OptimizationService()

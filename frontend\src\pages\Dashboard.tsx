import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { generalApi, mlApi, ragApi, chatApi } from '@/services/api'
import { useToast } from '@/hooks/use-toast'
import { 
  Activity, 
  Brain, 
  Database, 
  MessageSquare, 
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'

interface ServiceStatus {
  name: string
  status: 'healthy' | 'degraded' | 'error' | 'unknown'
  icon: React.ComponentType<any>
  description: string
}

export default function Dashboard() {
  const [services, setServices] = useState<ServiceStatus[]>([
    { name: 'ML Predictions', status: 'unknown', icon: Brain, description: 'Lambda, S3, DynamoDB cost predictions' },
    { name: 'RAG System', status: 'unknown', icon: Database, description: 'Document processing and retrieval' },
    { name: '<PERSON> Chat', status: 'unknown', icon: MessageSquare, description: 'Context-aware conversations' },
    { name: 'API Gateway', status: 'unknown', icon: Activity, description: 'Core API services' },
  ])
  
  const [stats, setStats] = useState({
    totalPredictions: 0,
    documentsProcessed: 0,
    activeConversations: 0,
    uptime: '99.9%'
  })

  const { toast } = useToast()

  useEffect(() => {
    checkServiceHealth()
  }, [])

  const checkServiceHealth = async () => {
    const updatedServices = [...services]

    try {
      // Check API Gateway
      await generalApi.getHealth()
      updatedServices[3].status = 'healthy'
    } catch {
      updatedServices[3].status = 'error'
    }

    try {
      // Check ML Service
      const mlStatus = await mlApi.getMLServiceStatus()
      updatedServices[0].status = mlStatus.data?.overall_status === 'healthy' ? 'healthy' : 'degraded'
      
    } catch {
      updatedServices[0].status = 'error'
    }

    try {
      // Check RAG Service
      const ragStatus = await ragApi.getRAGHealth()
      updatedServices[1].status = ragStatus.data?.overall_status === 'healthy' ? 'healthy' : 'degraded'
    } catch {
      updatedServices[1].status = 'error'
    }

    try {
      // Check Chat Service
      const chatStatus = await chatApi.getChatHealth()
      updatedServices[2].status = chatStatus.data?.overall_status === 'healthy' ? 'healthy' : 'degraded'
      
      // Get active conversations count
      const activeConversations = await chatApi.getActiveConversations()
      setStats(prev => ({
        ...prev,
        activeConversations: activeConversations.data?.count || 0
      }))
    } catch {
      updatedServices[2].status = 'error'
    }

    setServices(updatedServices)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'degraded':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      healthy: 'default',
      degraded: 'secondary',
      error: 'destructive',
      unknown: 'outline'
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const refreshServices = () => {
    checkServiceHealth()
    toast({
      title: "Services Refreshed",
      description: "Service health status has been updated.",
    })
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor your NOAH Arch system status and performance
          </p>
        </div>
        <Button onClick={refreshServices}>
          Refresh Status
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Predictions
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPredictions}</div>
            <p className="text-xs text-muted-foreground">
              ML model predictions made
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Documents Processed
            </CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.documentsProcessed}</div>
            <p className="text-xs text-muted-foreground">
              Documents in vector database
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Conversations
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeConversations}</div>
            <p className="text-xs text-muted-foreground">
              Ongoing chat sessions
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              System Uptime
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.uptime}</div>
            <p className="text-xs text-muted-foreground">
              Service availability
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Service Status */}
      <Card>
        <CardHeader>
          <CardTitle>Service Health</CardTitle>
          <CardDescription>
            Current status of all NOAH Arch services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {services.map((service) => {
              const Icon = service.icon
              return (
                <div key={service.name} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <Icon className="h-8 w-8 text-muted-foreground" />
                    <div>
                      <h3 className="font-medium">{service.name}</h3>
                      <p className="text-sm text-muted-foreground">{service.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(service.status)}
                    {getStatusBadge(service.status)}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks and shortcuts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Button variant="outline" className="h-20 flex-col">
              <Brain className="h-6 w-6 mb-2" />
              <span>Run Prediction</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Database className="h-6 w-6 mb-2" />
              <span>Upload Document</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <MessageSquare className="h-6 w-6 mb-2" />
              <span>Start Chat</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <TrendingUp className="h-6 w-6 mb-2" />
              <span>View Analytics</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

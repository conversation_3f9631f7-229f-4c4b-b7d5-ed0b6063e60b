import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'
import {
  PenTool,
  FileText,
  MessageSquare,
  Calculator,
  Users,
  Zap
} from 'lucide-react'

export default function Dashboard() {
  const { user } = useAuth()

  const handleNavigation = (path: string) => {
    window.location.href = path
  }

  const quickActions = [
    {
      title: 'Architecture Designer',
      description: 'Design and visualize cloud architectures',
      icon: PenTool,
      path: '/architecture',
      color: 'bg-blue-500'
    },
    {
      title: 'Requirements Analysis',
      description: 'Upload documents for AI-powered analysis',
      icon: FileText,
      path: '/requirements',
      color: 'bg-green-500'
    },
    // {
    //   title: 'AI Chat',
    //   description: 'Chat with AI about your architecture',
    //   icon: MessageSquare,
    //   path: '/chat',
    //   color: 'bg-purple-500'
    // },
    // {
    //   title: 'Cost Analysis',
    //   description: 'Analyze and optimize costs',
    //   icon: Calculator,
    //   path: '/cost-analysis',
    //   color: 'bg-orange-500'
    // }
  ]

  // Add admin portal for superusers
  if (user?.is_superuser) {
    quickActions.push({
      title: 'Admin Portal',
      description: 'Manage users and system settings',
      icon: Users,
      path: '/admin',
      color: 'bg-red-500'
    })
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight">Welcome to NoahArch</h1>
        <p className="text-xl text-muted-foreground">
          AI-Powered Cloud Architecture Designer
        </p>
        <p className="text-muted-foreground">
          Design, analyze, and optimize your cloud architectures with intelligent tools
        </p>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-center">Get Started</CardTitle>
          <CardDescription className="text-center">
            Choose an action to begin working with your cloud architectures
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {quickActions.map((action) => {
              const Icon = action.icon
              return (
                <Button
                  key={action.title}
                  variant="outline"
                  className="h-32 flex-col space-y-3 hover:shadow-lg transition-all"
                  onClick={() => handleNavigation(action.path)}
                >
                  <div className={`p-3 rounded-full ${action.color} text-white`}>
                    <Icon className="h-8 w-8" />
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">{action.title}</div>
                    <div className="text-sm text-muted-foreground">{action.description}</div>
                  </div>
                </Button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Features Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PenTool className="h-5 w-5" />
              Visual Design
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Drag-and-drop interface for designing cloud architectures with 100+ AWS services
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Cost Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Real-time cost predictions and optimization recommendations using ML models
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              AI-Powered
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Intelligent architecture generation from requirements and image analysis
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

# Architecture Chat Installation Guide

## 🔧 **Installation Steps**

### **1. Install Backend Dependencies**

The architecture chat functionality requires additional Python packages. Install them using pip:

```bash
# Navigate to backend directory
cd backend

# Activate virtual environment (if using one)
source venv/bin/activate  # On Linux/Mac
# OR
venv\Scripts\activate     # On Windows

# Install new dependencies
pip install langchain-openai==0.1.1
pip install duckduckgo-search==5.3.0

# OR install all requirements (recommended)
pip install -r requirements.txt
```

### **2. Verify Installation**

Check if the packages are installed correctly:

```bash
pip list | grep langchain
pip list | grep duckduckgo
```

You should see:
- `langchain-openai==0.1.1`
- `duckduckgo-search==5.3.0`

### **3. Environment Configuration**

Make sure your Azure OpenAI credentials are properly configured in your environment variables or `.env` file:

```env
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name
AZURE_OPENAI_TIMEOUT_SECONDS=30
```

### **4. Start the Backend Server**

```bash
# From backend directory
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### **5. Test the Installation**

Once the server starts, you can test the architecture chat endpoints:

1. **Health Check**: `GET http://localhost:8000/api/v1/architecture-chat/health`
2. **Chat Test**: `POST http://localhost:8000/api/v1/architecture-chat/chat`

## 🐛 **Troubleshooting**

### **Common Issues & Solutions**

#### **1. ModuleNotFoundError: No module named 'app.core.auth'**
✅ **FIXED**: Updated import to use `app.auth.dependencies`

#### **2. ImportError: cannot import name 'BaseMessage' from 'langchain.schema'**
✅ **FIXED**: Updated imports to use `langchain_core.messages`

#### **3. LangChain Version Conflicts**
If you encounter version conflicts:
```bash
pip uninstall langchain langchain-core langchain-community langchain-openai
pip install langchain==0.1.12 langchain-core==0.1.42 langchain-community==0.0.32 langchain-openai==0.1.1
```

#### **4. DuckDuckGo Search Issues**
If web search doesn't work:
```bash
pip uninstall duckduckgo-search
pip install duckduckgo-search==5.3.0
```

#### **5. Azure OpenAI Connection Issues**
- Verify your API key and endpoint are correct
- Check if your Azure OpenAI deployment is active
- Ensure the deployment name matches your configuration

### **6. Memory Issues**
If you encounter memory issues with large architectures:
- Reduce the conversation buffer size in `ArchitectureChatService.__init__()`:
```python
self.memory = ConversationBufferWindowMemory(
    memory_key="chat_history",
    return_messages=True,
    k=5  # Reduced from 10 to 5
)
```

## 📋 **Dependencies Summary**

### **New Backend Dependencies**
```
langchain-openai==0.1.1      # Azure OpenAI integration for LangChain
duckduckgo-search==5.3.0     # Web search functionality
```

### **Existing Dependencies (Required)**
```
langchain==0.1.12            # Core LangChain framework
langchain-core==0.1.42       # LangChain core components
langchain-community==0.0.32  # Community tools and utilities
```

## 🚀 **Verification Steps**

### **1. Backend Health Check**
```bash
curl http://localhost:8000/api/v1/architecture-chat/health
```

Expected response:
```json
{
  "status": "healthy",
  "service": "architecture_chat",
  "tools_available": 4,
  "llm_initialized": true,
  "agent_initialized": true
}
```

### **2. Simple Chat Test**
```bash
curl -X POST http://localhost:8000/api/v1/architecture-chat/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "user_query": "Hello, can you help me with AWS architecture?"
  }'
```

### **3. Frontend Integration Test**
1. Open the Architecture Designer page
2. Create or load an architecture
3. Click the "AI Chat" button in the toolbar
4. Send a test message like "Analyze my architecture"

## 📝 **Notes**

- **Authentication Required**: All chat endpoints require valid JWT authentication
- **Azure OpenAI Required**: The service requires Azure OpenAI credentials to function
- **Internet Connection**: Web search functionality requires internet access
- **Memory Usage**: The service keeps conversation history in memory (configurable)

## 🔄 **Updates Made**

### **Fixed Import Issues**
1. Changed `app.core.auth` → `app.auth.dependencies`
2. Changed `app.models.user` → `app.database.models`
3. Updated LangChain imports to use `langchain_core` modules

### **Fixed Async Issues**
1. Made optimization tool function synchronous
2. Used `asyncio.run()` for async calls within sync context
3. Simplified tool initialization

### **Added Error Handling**
1. Comprehensive error handling in all service methods
2. User-friendly error messages
3. Proper logging throughout the service

The architecture chat system should now work correctly after installing the dependencies and starting the backend server!

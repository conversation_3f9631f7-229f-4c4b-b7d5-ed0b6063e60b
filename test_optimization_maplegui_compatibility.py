#!/usr/bin/env python3
"""
Test script to verify optimization service follows MapleGUI logic exactly
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.optimization_service import OptimizationService

async def test_ec2_optimization():
    """Test that EC2 optimization suggests AWS Lambda (MapleGUI rule)"""
    print("🔍 Testing EC2 Optimization (MapleGUI Rule)")
    print("=" * 50)
    
    optimization_service = OptimizationService()
    
    # Test EC2 service alternatives generation
    target_service = "Amazon EC2"
    alternatives = await optimization_service._generate_service_alternatives(target_service)
    
    print(f"Target Service: {target_service}")
    print(f"Generated Alternatives: {alternatives}")
    
    # Verify MapleGUI rule: EC2 should suggest AWS Lambda
    if "AWS Lambda" in alternatives:
        print("✅ PASS: AWS Lambda is suggested for EC2 (MapleGUI rule applied)")
    else:
        print("❌ FAIL: AWS Lambda is NOT suggested for EC2 (MapleGUI rule not applied)")
    
    # Verify Lightsail is NOT suggested
    lightsail_variants = ["Amazon Lightsail", "Lightsail", "AWS Lightsail"]
    lightsail_found = any(variant in alternatives for variant in lightsail_variants)
    
    if not lightsail_found:
        print("✅ PASS: Lightsail is NOT suggested (correct MapleGUI behavior)")
    else:
        print("❌ FAIL: Lightsail is suggested (incorrect - not in MapleGUI)")
        print(f"   Found Lightsail variants: {[alt for alt in alternatives if any(variant in alt for variant in lightsail_variants)]}")
    
    print()
    return alternatives

async def test_s3_optimization():
    """Test that S3 optimization suggests Glacier (MapleGUI rule)"""
    print("🔍 Testing S3 Optimization (MapleGUI Rule)")
    print("=" * 50)
    
    optimization_service = OptimizationService()
    
    # Test S3 service alternatives generation
    target_service = "Amazon S3"
    alternatives = await optimization_service._generate_service_alternatives(target_service)
    
    print(f"Target Service: {target_service}")
    print(f"Generated Alternatives: {alternatives}")
    
    # Verify MapleGUI rule: S3 should suggest Glacier
    glacier_variants = ["Amazon S3 Glacier", "Glacier", "S3 Glacier"]
    glacier_found = any(variant in alternatives for variant in glacier_variants)
    
    if glacier_found:
        print("✅ PASS: Glacier is suggested for S3 (MapleGUI rule applied)")
    else:
        print("❌ FAIL: Glacier is NOT suggested for S3 (MapleGUI rule not applied)")
    
    print()
    return alternatives

async def test_dynamodb_optimization():
    """Test that DynamoDB optimization suggests only DAX (MapleGUI rule)"""
    print("🔍 Testing DynamoDB Optimization (MapleGUI Rule)")
    print("=" * 50)
    
    optimization_service = OptimizationService()
    
    # Test DynamoDB service alternatives generation
    target_service = "Amazon DynamoDB"
    alternatives = await optimization_service._generate_service_alternatives(target_service)
    
    print(f"Target Service: {target_service}")
    print(f"Generated Alternatives: {alternatives}")
    
    # Verify MapleGUI rule: DynamoDB should suggest only DAX
    if "Amazon DynamoDB Accelerator (DAX)" in alternatives:
        print("✅ PASS: DAX is suggested for DynamoDB (MapleGUI rule applied)")
    else:
        print("❌ FAIL: DAX is NOT suggested for DynamoDB (MapleGUI rule not applied)")
    
    # Verify only DAX is suggested (MapleGUI replaces all alternatives with DAX)
    if len(alternatives) == 1 and alternatives[0] == "Amazon DynamoDB Accelerator (DAX)":
        print("✅ PASS: Only DAX is suggested (correct MapleGUI behavior)")
    else:
        print("❌ FAIL: Multiple alternatives suggested (incorrect - MapleGUI only suggests DAX)")
        print(f"   Expected: ['Amazon DynamoDB Accelerator (DAX)']")
        print(f"   Actual: {alternatives}")
    
    print()
    return alternatives

async def test_service_filtering():
    """Test that service filtering removes non-MapleGUI services"""
    print("🔍 Testing Service Filtering (MapleGUI Compatibility)")
    print("=" * 50)
    
    optimization_service = OptimizationService()
    
    # Test with services including Lightsail (should be filtered out)
    test_services = [
        "Amazon EC2",
        "AWS Lambda", 
        "Amazon Lightsail",  # Should be filtered out
        "Amazon S3",
        "Some Random Service"  # Should be filtered out
    ]
    
    filtered_services = optimization_service._filter_to_maplegui_services(test_services)
    
    print(f"Input Services: {test_services}")
    print(f"Filtered Services: {filtered_services}")
    
    # Verify Lightsail is filtered out
    if "Amazon Lightsail" not in filtered_services:
        print("✅ PASS: Lightsail is filtered out (correct)")
    else:
        print("❌ FAIL: Lightsail is NOT filtered out (incorrect)")
    
    # Verify valid MapleGUI services are kept
    valid_services = ["Amazon EC2", "AWS Lambda", "Amazon S3"]
    all_valid_kept = all(service in filtered_services for service in valid_services)
    
    if all_valid_kept:
        print("✅ PASS: Valid MapleGUI services are kept")
    else:
        print("❌ FAIL: Some valid MapleGUI services were filtered out")
        missing = [s for s in valid_services if s not in filtered_services]
        print(f"   Missing services: {missing}")
    
    print()
    return filtered_services

async def main():
    """Run all optimization tests"""
    print("🚀 MapleGUI Optimization Compatibility Test")
    print("=" * 60)
    print()
    
    try:
        # Test individual service optimization rules
        ec2_alternatives = await test_ec2_optimization()
        s3_alternatives = await test_s3_optimization()
        dynamodb_alternatives = await test_dynamodb_optimization()
        
        # Test service filtering
        filtered_services = await test_service_filtering()
        
        print("📊 SUMMARY")
        print("=" * 50)
        print(f"EC2 alternatives: {ec2_alternatives}")
        print(f"S3 alternatives: {s3_alternatives}")
        print(f"DynamoDB alternatives: {dynamodb_alternatives}")
        print(f"Service filtering test: {len(filtered_services)} services kept")
        
        # Overall assessment
        print()
        print("🎯 MAPLEGUI COMPATIBILITY ASSESSMENT")
        print("=" * 50)
        
        # Check if all MapleGUI rules are followed
        ec2_correct = "AWS Lambda" in ec2_alternatives and not any("Lightsail" in alt for alt in ec2_alternatives)
        s3_correct = any("Glacier" in alt for alt in s3_alternatives)
        dynamodb_correct = len(dynamodb_alternatives) == 1 and "DAX" in dynamodb_alternatives[0]
        
        if ec2_correct and s3_correct and dynamodb_correct:
            print("✅ ALL TESTS PASSED: Optimization service follows MapleGUI logic exactly")
        else:
            print("❌ SOME TESTS FAILED: Optimization service does not match MapleGUI")
            if not ec2_correct:
                print("   - EC2 optimization rule failed")
            if not s3_correct:
                print("   - S3 optimization rule failed")
            if not dynamodb_correct:
                print("   - DynamoDB optimization rule failed")
        
    except Exception as e:
        print(f"❌ ERROR: Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())

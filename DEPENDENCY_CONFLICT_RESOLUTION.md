# Dependency Conflict Resolution Guide

## 🚨 **Problem Summary**
You're experiencing dependency conflicts due to incompatible package versions:
- **Pydantic**: Need 2.7.0+ but have 1.10.17
- **LangChain**: Core version too old for newer packages
- **LangGraph**: Installed but not needed (causing conflicts)

## 🔧 **Step-by-Step Resolution**

### **Option 1: Clean Reinstall (Recommended)**

#### **Step 1: Backup and Clean Environment**
```bash
# Navigate to backend directory
cd backend

# Deactivate virtual environment if active
deactivate

# Remove existing virtual environment
rm -rf venv  # Linux/Mac
# OR
rmdir /s venv  # Windows

# Create fresh virtual environment
python -m venv venv

# Activate new environment
source venv/bin/activate  # Linux/Mac
# OR
venv\Scripts\activate  # Windows
```

#### **Step 2: Install Updated Requirements**
```bash
# Install updated requirements with compatible versions
pip install -r requirements.txt

# If you get conflicts, force reinstall:
pip install --force-reinstall -r requirements.txt
```

### **Option 2: Selective Update (If Clean Install Not Possible)**

#### **Step 1: Remove Conflicting Packages**
```bash
# Remove LangGraph packages (not needed for our implementation)
pip uninstall langgraph-prebuilt langgraph-checkpoint langgraph -y

# Remove old LangChain packages
pip uninstall langchain langchain-core langchain-community langchain-openai -y

# Remove old Pydantic
pip uninstall pydantic pydantic-settings -y
```

#### **Step 2: Install Compatible Versions**
```bash
# Install Pydantic 2.x first
pip install pydantic==2.7.4 pydantic-settings==2.10.1

# Install updated LangChain packages
pip install langchain==0.2.16 langchain-core==0.2.38 langchain-community==0.2.16 langchain-openai==0.1.23

# Install web search
pip install duckduckgo-search==5.3.0

# Update FastAPI for Pydantic 2.x compatibility
pip install fastapi==0.115.0 uvicorn==0.30.0
```

## 📋 **Updated Package Versions**

### **Key Changes Made:**
```
# OLD (Causing Conflicts)          # NEW (Compatible)
pydantic==1.10.17          →       pydantic==2.7.4
fastapi==0.104.1           →       fastapi==0.115.0
uvicorn==0.23.2            →       uvicorn==0.30.0
langchain==0.1.12          →       langchain==0.2.16
langchain-core==0.1.42     →       langchain-core==0.2.38
langchain-community==0.0.32 →      langchain-community==0.2.16
langchain-openai==0.1.1    →       langchain-openai==0.1.23
```

### **Removed Packages (Not Needed):**
- `langgraph-prebuilt` - Not used in our LangChain implementation
- `langgraph-checkpoint` - Not used in our LangChain implementation
- `langgraph` - We're using LangChain, not LangGraph

## 🔄 **Code Updates Required**

Due to Pydantic 2.x changes, you may need to update some code:

### **1. Pydantic Model Updates**
If you have any Pydantic models, update them:

```python
# OLD (Pydantic 1.x)
from pydantic import BaseModel

class MyModel(BaseModel):
    class Config:
        orm_mode = True

# NEW (Pydantic 2.x)
from pydantic import BaseModel, ConfigDict

class MyModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)
```

### **2. FastAPI Response Model Updates**
```python
# OLD
@app.get("/", response_model=MyModel)

# NEW (if needed)
@app.get("/", response_model=MyModel)  # Usually no change needed
```

## 🧪 **Verification Steps**

### **Step 1: Check Installation**
```bash
pip list | grep -E "(pydantic|langchain|fastapi)"
```

Expected output:
```
fastapi                    0.115.0
langchain                  0.2.16
langchain-community        0.2.16
langchain-core             0.2.38
langchain-openai           0.1.23
pydantic                   2.7.4
pydantic-settings          2.10.1
```

### **Step 2: Test Import**
```bash
python -c "
from app.services.architecture_chat_service import architecture_chat_service
from app.api.v1.endpoints.architecture_chat import router
print('✅ All imports successful!')
"
```

### **Step 3: Start Server**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 🚨 **If You Still Get Conflicts**

### **Nuclear Option: Complete Reset**
```bash
# Remove everything and start fresh
pip freeze > old_requirements.txt
pip uninstall -r old_requirements.txt -y
pip install -r requirements.txt
```

### **Alternative: Use pip-tools**
```bash
# Install pip-tools for better dependency resolution
pip install pip-tools

# Generate lock file
pip-compile requirements.txt

# Install from lock file
pip-sync requirements.txt
```

## 📝 **Common Issues & Solutions**

### **Issue 1: "Cannot uninstall package"**
```bash
pip install --force-reinstall --no-deps package_name
```

### **Issue 2: "Package not found"**
```bash
pip install --upgrade pip
pip cache purge
pip install package_name
```

### **Issue 3: "Permission denied"**
```bash
# Use --user flag
pip install --user package_name

# Or run with sudo (Linux/Mac)
sudo pip install package_name
```

## ✅ **Success Indicators**

After successful resolution:
1. ✅ No dependency conflict warnings
2. ✅ Backend starts without import errors
3. ✅ Architecture chat endpoints respond
4. ✅ Frontend chat button works

## 🎯 **Next Steps After Resolution**

1. **Test the chat functionality**:
   - Open Architecture Designer
   - Click "AI Chat" button
   - Send test message

2. **Verify all features work**:
   - Architecture analysis
   - Cost calculation
   - Web search
   - Optimization suggestions

The updated requirements.txt should resolve all dependency conflicts and provide a stable foundation for the architecture chat functionality!

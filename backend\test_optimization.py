#!/usr/bin/env python3
"""
Test script for the Optimize Architecture feature

This script tests the complete optimization workflow to ensure feature parity
with the PyQt5 MapleGUI "Optimize Architecture" functionality.
"""

import asyncio
import json
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.optimization_service import optimization_service
from services.ml_service import ml_service


async def test_optimization_service():
    """Test the optimization service with various scenarios"""
    
    print("🧪 Testing Optimize Architecture Feature")
    print("=" * 50)
    
    # Test Case 1: Lambda Optimization
    print("\n📋 Test Case 1: AWS Lambda Optimization")
    lambda_request = {
        'target_service': 'AWS Lambda',
        'throughput': '1000',
        'latency_requirement': '5.0',
        'memory_requirement': '1024',
        'current_memory': '512',
        'current_cores': '1',
        'unit': 'second',
        'nodes': [
            {
                'id': 'lambda-1',
                'data': {
                    'service': {'name': 'AWS Lambda'},
                    'label': 'AWS Lambda',
                    'config': {'memory': 512}
                }
            }
        ],
        'edges': []
    }
    
    result = await optimization_service.optimize_architecture(lambda_request)
    print(f"Status: {result['status']}")
    if result['status'] == 'success':
        print(f"Target Service: {result['target_service']}")
        print(f"Alternatives Analyzed: {result['alternatives_analyzed']}")
        print(f"Recommendation Action: {result['recommendation']['action']}")
        print(f"Recommendation: {result['recommendation']['recommendation_text']}")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")
    
    # Test Case 2: EC2 Optimization
    print("\n📋 Test Case 2: Amazon EC2 Optimization")
    ec2_request = {
        'target_service': 'Amazon EC2',
        'throughput': '500',
        'latency_requirement': '2.0',
        'memory_requirement': '2048',
        'current_memory': '1024',
        'current_cores': '2',
        'unit': 'second',
        'nodes': [
            {
                'id': 'ec2-1',
                'data': {
                    'service': {'name': 'Amazon EC2'},
                    'label': 'Amazon EC2',
                    'config': {'memory': 1024, 'cores': 2}
                }
            }
        ],
        'edges': []
    }
    
    result = await optimization_service.optimize_architecture(ec2_request)
    print(f"Status: {result['status']}")
    if result['status'] == 'success':
        print(f"Target Service: {result['target_service']}")
        print(f"Alternatives Analyzed: {result['alternatives_analyzed']}")
        print(f"Recommendation Action: {result['recommendation']['action']}")
        print(f"Recommendation: {result['recommendation']['recommendation_text']}")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")
    
    # Test Case 3: DynamoDB Optimization
    print("\n📋 Test Case 3: Amazon DynamoDB Optimization")
    dynamodb_request = {
        'target_service': 'Amazon DynamoDB',
        'throughput': '2000',
        'latency_requirement': '1.0',
        'memory_requirement': '1024',
        'current_memory': '1024',
        'current_cores': '2',
        'unit': 'second',
        'nodes': [
            {
                'id': 'dynamodb-1',
                'data': {
                    'service': {'name': 'Amazon DynamoDB'},
                    'label': 'Amazon DynamoDB',
                    'config': {'memory': 1024}
                }
            }
        ],
        'edges': []
    }
    
    result = await optimization_service.optimize_architecture(dynamodb_request)
    print(f"Status: {result['status']}")
    if result['status'] == 'success':
        print(f"Target Service: {result['target_service']}")
        print(f"Alternatives Analyzed: {result['alternatives_analyzed']}")
        print(f"Recommendation Action: {result['recommendation']['action']}")
        print(f"Recommendation: {result['recommendation']['recommendation_text']}")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")
    
    # Test Case 4: ElastiCache Optimization
    print("\n📋 Test Case 4: Amazon ElastiCache Optimization")
    elasticache_request = {
        'target_service': 'Amazon ElastiCache',
        'throughput': '1500',
        'latency_requirement': '0.5',
        'memory_requirement': '2048',
        'current_memory': '1024',
        'current_cores': '1',
        'unit': 'second',
        'nodes': [
            {
                'id': 'elasticache-1',
                'data': {
                    'service': {'name': 'Amazon ElastiCache'},
                    'label': 'Amazon ElastiCache',
                    'config': {'memory': 1024, 'cores': 1}
                }
            }
        ],
        'edges': []
    }
    
    result = await optimization_service.optimize_architecture(elasticache_request)
    print(f"Status: {result['status']}")
    if result['status'] == 'success':
        print(f"Target Service: {result['target_service']}")
        print(f"Alternatives Analyzed: {result['alternatives_analyzed']}")
        print(f"Recommendation Action: {result['recommendation']['action']}")
        print(f"Recommendation: {result['recommendation']['recommendation_text']}")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")


def test_ml_functions():
    """Test the ML functions used in optimization"""
    
    print("\n🔬 Testing ML Functions")
    print("=" * 30)
    
    # Test Lambda ML function
    print("\n🔹 Testing Lambda ML Function")
    lambda_result = ml_service.predict_lambda_cost(
        workload_invocations=1000,
        memory_mb=1024,
        function_defn="DTS_deepreader",
        memory_required=1024
    )
    print(f"Lambda Result: {lambda_result}")
    
    # Test S3 ML function
    print("\n🔹 Testing S3 ML Function")
    s3_result = ml_service.predict_s3_cost(
        workload=1000,
        file_size=20480,
        memory=1024,
        operation="read"
    )
    print(f"S3 Result: {s3_result}")
    
    # Test DynamoDB ML function
    print("\n🔹 Testing DynamoDB ML Function")
    dynamodb_result = ml_service.predict_dynamodb_cost(
        workload=1000,
        data_size=100000,
        chunk_size=10000
    )
    print(f"DynamoDB Result: {dynamodb_result}")
    
    # Test ElastiCache ML function
    print("\n🔹 Testing ElastiCache ML Function")
    elasticache_result = ml_service.predict_elasticache_cost(
        workload=1000,
        instance_type='t3.micro'
    )
    print(f"ElastiCache Result: {elasticache_result}")


async def test_service_alternatives():
    """Test service alternatives generation"""
    
    print("\n🔄 Testing Service Alternatives Generation")
    print("=" * 40)
    
    services_to_test = [
        'Amazon EC2',
        'AWS Lambda',
        'Amazon S3',
        'Amazon DynamoDB',
        'Amazon ElastiCache'
    ]
    
    for service in services_to_test:
        print(f"\n🔹 Alternatives for {service}:")
        alternatives = await optimization_service._generate_service_alternatives(service)
        for i, alt in enumerate(alternatives, 1):
            print(f"  {i}. {alt}")


def test_optimization_validation():
    """Test optimization request validation"""
    
    print("\n✅ Testing Optimization Request Validation")
    print("=" * 45)
    
    # Valid request
    valid_request = {
        'target_service': 'AWS Lambda',
        'throughput': '1000',
        'latency_requirement': '5.0',
        'memory_requirement': '1024',
        'current_memory': '512',
        'current_cores': '1',
        'unit': 'second',
        'nodes': [],
        'edges': []
    }
    
    print("✅ Valid request should pass validation")
    
    # Invalid requests
    invalid_requests = [
        {**valid_request, 'target_service': ''},  # Missing target service
        {**valid_request, 'throughput': ''},      # Missing throughput
        {**valid_request, 'throughput': 'invalid'},  # Invalid throughput
        {**valid_request, 'latency_requirement': ''},  # Missing latency
        {**valid_request, 'unit': 'invalid'},    # Invalid unit
    ]
    
    for i, invalid_req in enumerate(invalid_requests, 1):
        print(f"❌ Invalid request {i} should fail validation")


async def main():
    """Run all tests"""
    
    print("🚀 Starting Optimize Architecture Feature Tests")
    print("=" * 60)
    
    try:
        # Test ML functions first
        test_ml_functions()
        
        # Test optimization validation
        test_optimization_validation()
        
        # Test service alternatives
        await test_service_alternatives()
        
        # Test optimization service
        await test_optimization_service()
        
        print("\n✅ All tests completed successfully!")
        print("🎉 Optimize Architecture feature is ready for use!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

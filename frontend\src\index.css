@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced custom scrollbar styles */
.custom-scrollbar {
  /* Fallback for Firefox */
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 #f1f5f9;
}

.dark .custom-scrollbar {
  scrollbar-color: #60a5fa #1e293b;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 10px;
  margin: 2px;
}

.dark .custom-scrollbar::-webkit-scrollbar-track {
  background: #1e293b;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  min-height: 20px;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #60a5fa, #a78bfa);
  border-color: #475569;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #7c3aed);
  border-color: #cbd5e1;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-color: #64748b;
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

.dark .custom-scrollbar::-webkit-scrollbar-corner {
  background: #1e293b;
}

/* Force scrollbar visibility */
.custom-scrollbar {
  overflow-y: scroll !important;
}

/* Alternative scrollbar for better visibility */
.scrollbar-visible {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 #f1f5f9;
  overflow-y: auto;
}

.scrollbar-visible::-webkit-scrollbar {
  width: 10px;
  background: #f1f5f9;
}

.scrollbar-visible::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 5px;
  border: 2px solid #f1f5f9;
}

.scrollbar-visible::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}

/* Animation keyframes */
@keyframes animate-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-in {
  animation: animate-in 0.3s ease-out;
}

.slide-in-from-bottom-4 {
  animation: slide-in-from-bottom-4 0.5s ease-out;
}

@keyframes slide-in-from-bottom-4 {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Dark mode enhancements for React Flow and Architecture Canvas */
.dark .react-flow__background {
  background-color: hsl(var(--background));
}

.dark .react-flow__minimap {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

.dark .react-flow__controls {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

.dark .react-flow__controls button {
  background-color: hsl(var(--card));
  border-bottom: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
}

.dark .react-flow__controls button:hover {
  background-color: hsl(var(--accent));
}

/* Dark mode for architecture nodes */
.dark .architecture-node {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
  color: hsl(var(--card-foreground));
}

.dark .architecture-node.selected {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
}

/* Dark mode for edges */
.dark .react-flow__edge-path {
  stroke: hsl(var(--muted-foreground));
}

.dark .react-flow__edge.selected .react-flow__edge-path {
  stroke: hsl(var(--primary));
}

/* Dark mode for component palette */
.dark .component-palette-item {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
  color: hsl(var(--card-foreground));
}

.dark .component-palette-item:hover {
  background-color: hsl(var(--accent));
}

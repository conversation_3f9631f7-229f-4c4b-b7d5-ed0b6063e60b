"""
ElastiCache ML prediction functions

This module replicates the PyQt5 MapleGUI ElastiCache cost and latency prediction functionality,
providing ML-based predictions for Amazon ElastiCache optimization.
"""

import math
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class ElastiCachePredictor:
    """
    ElastiCache cost and latency predictor
    
    Replicates the exact functionality from PyQt5 MapleGUI compute_elasticache_latency_cost function
    """
    
    def __init__(self):
        """Initialize the ElastiCache predictor"""
        # ElastiCache instance type configurations (from PyQt5)
        self.elasticache_cost_rate = {
            't2.micro': 0.017,
            't3.micro': 0.017,
            't2.small': 0.034,
            't3.small': 0.034
        }
        
        self.elasticache_concurrency = {
            't2.micro': 1000,
            't3.micro': 1000,
            't2.small': 6500,
            't3.small': 6500
        }
        
        # Latency models for different instance types (from PyQt5)
        self.checkout_latency_models = {
            't3.micro': lambda x: 1.3056 * math.log(x) + 9.0416,
            't2.small': lambda x: 1.4299 * math.log(x) + 7.3159,
            't2.micro': lambda x: 1.9081 * math.log(x) + 10.646,
            't3.small': lambda x: 1.4554 * math.log(x) + 10.114,
        }
    
    def compute_elasticache_latency_and_cost(self, input_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compute ElastiCache latency and cost
        
        This function replicates the exact PyQt5 MapleGUI compute_elasticache_latency_cost function
        
        Args:
            input_params: Dictionary containing:
                - workload or concurrency: Number of concurrent requests
                - elasticache_instance_type: Instance type (t2.micro, t2.small, t3.micro, t3.small)
                
        Returns:
            Dictionary containing latency and cost information
        """
        try:
            # Set defaults (from PyQt5)
            defaults = {
                'workload': 1,
                'workflow': 1,
                'mysql_instance_type': 'db.t3.medium',
                'elasticache_instance_type': 't3.micro',
                'eks_num_nodes': 1,
                'eks_instance_type': 'm5.large',
                'postgresql_instance_type': 'db.t3.medium',
                'mq_instance_type': 't3.micro'
            }
            
            # Update defaults with values from input_params
            params = {**defaults, **input_params}
            
            # Extract parameters
            concurrency = params.get('workload', params.get('concurrency', 1))
            elasticache_instance_type = params.get('elasticache_instance_type', 't3.micro')
            
            # Calculate latency using PyQt5 formula
            x = concurrency / 1000
            
            # Use the appropriate latency model for the instance type
            if elasticache_instance_type in self.checkout_latency_models:
                latency = self.checkout_latency_models[elasticache_instance_type](x)
            else:
                # Default to t3.micro if instance type not found
                latency = self.checkout_latency_models['t3.micro'](x)
            
            # Handle negative latency (from PyQt5 logic)
            if latency < 0:
                latency = concurrency * 0.0654252
            
            # Calculate cost (from PyQt5 logic)
            elasticache_num_nodes = math.ceil(
                concurrency / self.elasticache_concurrency.get(elasticache_instance_type, 1000)
            )
            elasticache_cost = (
                elasticache_num_nodes * 
                self.elasticache_cost_rate.get(elasticache_instance_type, 0.017)
            )
            
            return {
                'latency': latency,
                'cost': elasticache_cost,
                'instance_type': elasticache_instance_type,
                'num_nodes': elasticache_num_nodes,
                'concurrency': concurrency
            }
            
        except Exception as e:
            logger.error(f"Error in ElastiCache prediction: {str(e)}")
            # Return fallback values
            return {
                'latency': 10.0,  # Default latency
                'cost': 0.017,    # Default t2.micro cost
                'instance_type': 't3.micro',
                'num_nodes': 1,
                'concurrency': 1,
                'error': str(e)
            }
    
    def get_instance_configurations(self) -> Dict[str, Dict[str, Any]]:
        """
        Get available ElastiCache instance configurations
        
        Returns:
            Dictionary mapping instance types to their configurations
        """
        configurations = {}
        
        for instance_type in self.elasticache_cost_rate.keys():
            configurations[instance_type] = {
                'cost_per_hour': self.elasticache_cost_rate[instance_type],
                'max_concurrency': self.elasticache_concurrency[instance_type],
                'memory_mb': self._get_instance_memory(instance_type),
                'cores': self._get_instance_cores(instance_type)
            }
        
        return configurations
    
    def _get_instance_memory(self, instance_type: str) -> int:
        """
        Get memory configuration for instance type
        """
        memory_map = {
            't2.micro': 1024,   # 1 GB
            't3.micro': 1024,   # 1 GB
            't2.small': 2048,   # 2 GB
            't3.small': 2048    # 2 GB
        }
        return memory_map.get(instance_type, 1024)
    
    def _get_instance_cores(self, instance_type: str) -> int:
        """
        Get CPU cores for instance type
        """
        cores_map = {
            't2.micro': 1,
            't3.micro': 2,
            't2.small': 1,
            't3.small': 2
        }
        return cores_map.get(instance_type, 1)
    
    def optimize_instance_type(self, workload: int, latency_requirement: float) -> Dict[str, Any]:
        """
        Find optimal ElastiCache instance type for given workload and latency requirement
        
        Args:
            workload: Number of concurrent requests
            latency_requirement: Maximum acceptable latency in seconds
            
        Returns:
            Dictionary containing optimization recommendation
        """
        try:
            best_option = None
            best_cost = float('inf')
            
            for instance_type in self.elasticache_cost_rate.keys():
                result = self.compute_elasticache_latency_and_cost({
                    'workload': workload,
                    'elasticache_instance_type': instance_type
                })
                
                # Check if this option meets latency requirement
                if result['latency'] <= latency_requirement:
                    if result['cost'] < best_cost:
                        best_cost = result['cost']
                        best_option = {
                            'instance_type': instance_type,
                            'latency': result['latency'],
                            'cost': result['cost'],
                            'num_nodes': result['num_nodes']
                        }
            
            if best_option:
                return {
                    'status': 'success',
                    'recommendation': best_option,
                    'meets_requirements': True
                }
            else:
                # Find option with lowest latency if none meet requirements
                lowest_latency_option = None
                lowest_latency = float('inf')
                
                for instance_type in self.elasticache_cost_rate.keys():
                    result = self.compute_elasticache_latency_and_cost({
                        'workload': workload,
                        'elasticache_instance_type': instance_type
                    })
                    
                    if result['latency'] < lowest_latency:
                        lowest_latency = result['latency']
                        lowest_latency_option = {
                            'instance_type': instance_type,
                            'latency': result['latency'],
                            'cost': result['cost'],
                            'num_nodes': result['num_nodes']
                        }
                
                return {
                    'status': 'success',
                    'recommendation': lowest_latency_option,
                    'meets_requirements': False,
                    'note': 'No configuration meets latency requirement. Showing lowest latency option.'
                }
                
        except Exception as e:
            logger.error(f"Error in ElastiCache optimization: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }


# Create global instance
elasticache_predictor = ElastiCachePredictor()

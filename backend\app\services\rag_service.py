"""
RAG service for document processing and question answering
"""

import os
import uuid
from typing import Dict, Any, List
from fastapi import UploadFile
from app.ml_functions.rag_functions import RAGService as RAGCore
from app.core.config import settings


class RAGService:
    """RAG service wrapper for FastAPI integration"""
    
    def __init__(self):
        """Initialize the RAG service"""
        self.rag_core = RAGCore()
        self.conversation_history = {}  # Store conversation history by conversation_id
        
        # Ensure upload directory exists
        os.makedirs(settings.UPLOAD_DIRECTORY, exist_ok=True)
        print("RAG Service initialized")
    
    async def upload_document(self, file: UploadFile, chunk_size: int = 500, 
                            chunk_overlap: int = 50) -> Dict[str, Any]:
        """
        Upload and process a document
        
        Args:
            file: Uploaded file
            chunk_size: Size of text chunks
            chunk_overlap: Overlap between chunks
            
        Returns:
            Dictionary containing upload results
        """
        try:
            # Validate file type
            file_extension = os.path.splitext(file.filename)[1].lower()
            if file_extension not in settings.ALLOWED_FILE_TYPES:
                return {
                    'status': 'error',
                    'error': f'File type {file_extension} not allowed. Allowed types: {settings.ALLOWED_FILE_TYPES}'
                }
            
            # Validate file size
            file_content = await file.read()
            if len(file_content) > settings.MAX_FILE_SIZE:
                return {
                    'status': 'error',
                    'error': f'File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes'
                }
            
            # Generate unique document ID
            document_id = str(uuid.uuid4())
            
            # Save file temporarily
            file_path = os.path.join(settings.UPLOAD_DIRECTORY, f"{document_id}_{file.filename}")
            with open(file_path, "wb") as f:
                f.write(file_content)
            
            # Process the document
            result = self.rag_core.process_document(file_path, chunk_size, chunk_overlap)
            
            # Clean up temporary file
            os.remove(file_path)
            
            if result['status'] == 'success':
                return {
                    'status': 'success',
                    'document_id': document_id,
                    'filename': file.filename,
                    'chunks_created': result['chunks_created'],
                    'embeddings_created': result['chunks_created'],  # Same as chunks for now
                    'message': 'Document uploaded and processed successfully'
                }
            else:
                return result
                
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': 'Failed to upload and process document'
            }
    
    def query_documents(self, query: str, top_k: int = 5, include_context: bool = True) -> Dict[str, Any]:
        """
        Query documents using RAG
        
        Args:
            query: Query string
            top_k: Number of top results to retrieve
            include_context: Whether to include context in response
            
        Returns:
            Dictionary containing query results
        """
        try:
            # Query the vector database
            search_result = self.rag_core.query_documents(query, top_k)
            
            if search_result['status'] != 'success':
                return search_result
            
            # Generate answer using retrieved context
            answer_result = self.rag_core.generate_answer(query, search_result['context'])
            
            if answer_result['status'] != 'success':
                return answer_result
            
            response = {
                'status': 'success',
                'answer': answer_result['answer'],
                'confidence': None,  # Could be implemented based on retrieval scores
                'sources': search_result['sources'] if include_context else None
            }
            
            if include_context:
                response['context'] = search_result['context']
            
            if 'tokens_used' in answer_result:
                response['tokens_used'] = answer_result['tokens_used']
            
            return response
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': 'Failed to query documents'
            }
    
    def chat_with_context(self, message: str, conversation_id: str = None, 
                         include_history: bool = True, max_tokens: int = 500) -> Dict[str, Any]:
        """
        Chat with AI using document context and conversation history
        
        Args:
            message: User message
            conversation_id: Conversation ID for context
            include_history: Whether to include conversation history
            max_tokens: Maximum tokens in response
            
        Returns:
            Dictionary containing chat response
        """
        try:
            # Generate conversation ID if not provided
            if conversation_id is None:
                conversation_id = str(uuid.uuid4())
            
            # Initialize conversation history if needed
            if conversation_id not in self.conversation_history:
                self.conversation_history[conversation_id] = []
            
            # Query documents for relevant context
            search_result = self.rag_core.query_documents(message, top_k=3)
            context = search_result.get('context', []) if search_result['status'] == 'success' else []
            
            # Prepare conversation context
            conversation_context = ""
            if include_history and self.conversation_history[conversation_id]:
                conversation_context = "\n".join([
                    f"User: {msg['user']}\nAssistant: {msg['assistant']}"
                    for msg in self.conversation_history[conversation_id][-3:]  # Last 3 exchanges
                ])
            
            # Combine document context and conversation context
            full_context = []
            if context:
                full_context.extend(context)
            if conversation_context:
                full_context.append(f"Previous conversation:\n{conversation_context}")
            
            # Generate response
            answer_result = self.rag_core.generate_answer(message, full_context, max_tokens)
            
            if answer_result['status'] != 'success':
                return answer_result
            
            # Store conversation history
            self.conversation_history[conversation_id].append({
                'user': message,
                'assistant': answer_result['answer']
            })
            
            # Limit conversation history to prevent memory issues
            if len(self.conversation_history[conversation_id]) > 10:
                self.conversation_history[conversation_id] = self.conversation_history[conversation_id][-10:]
            
            return {
                'status': 'success',
                'response': answer_result['answer'],
                'conversation_id': conversation_id,
                'tokens_used': answer_result.get('tokens_used')
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': 'Failed to process chat message'
            }
    
    def suggest_rag_architecture(self, requirements: Dict[str, str]) -> Dict[str, Any]:
        """
        Suggest optimal RAG architecture
        
        Args:
            requirements: Dictionary containing user requirements
            
        Returns:
            Dictionary containing architecture recommendations
        """
        try:
            result = self.rag_core.suggest_rag_architecture(requirements)
            return result
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': 'Failed to suggest RAG architecture'
            }
    
    def get_conversation_history(self, conversation_id: str) -> Dict[str, Any]:
        """
        Get conversation history for a given conversation ID
        
        Args:
            conversation_id: Conversation ID
            
        Returns:
            Dictionary containing conversation history
        """
        if conversation_id in self.conversation_history:
            return {
                'status': 'success',
                'conversation_id': conversation_id,
                'history': self.conversation_history[conversation_id]
            }
        else:
            return {
                'status': 'error',
                'error': 'Conversation not found'
            }
    
    def clear_conversation_history(self, conversation_id: str = None) -> Dict[str, Any]:
        """
        Clear conversation history
        
        Args:
            conversation_id: Specific conversation ID to clear, or None to clear all
            
        Returns:
            Dictionary containing operation result
        """
        try:
            if conversation_id:
                if conversation_id in self.conversation_history:
                    del self.conversation_history[conversation_id]
                    return {
                        'status': 'success',
                        'message': f'Conversation {conversation_id} cleared'
                    }
                else:
                    return {
                        'status': 'error',
                        'error': 'Conversation not found'
                    }
            else:
                self.conversation_history.clear()
                return {
                    'status': 'success',
                    'message': 'All conversations cleared'
                }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': 'Failed to clear conversation history'
            }


# Create global instance
rag_service = RAGService()

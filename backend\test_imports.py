#!/usr/bin/env python3
"""
Test script to verify all imports work correctly for the optimization feature
"""

def test_imports():
    """Test that all required services can be imported"""
    
    print("🧪 Testing Service Imports")
    print("=" * 30)
    
    try:
        print("✅ Testing ML service import...")
        from app.services.ml_service import ml_service
        print(f"   ML service imported successfully: {type(ml_service)}")
        
        print("✅ Testing RAG service import...")
        from app.services.rag_service import rag_service
        print(f"   RAG service imported successfully: {type(rag_service)}")
        
        print("✅ Testing VPC service import...")
        from app.services.vpc_service import vpc_service
        print(f"   VPC service imported successfully: {type(vpc_service)}")
        
        print("✅ Testing AWS worksheet service import...")
        from app.services.aws_worksheet_service import aws_worksheet_service
        print(f"   AWS worksheet service imported successfully: {type(aws_worksheet_service)}")
        
        print("✅ Testing Excel worksheet service import...")
        from app.services.excel_worksheet_service import excel_worksheet_service
        print(f"   Excel worksheet service imported successfully: {type(excel_worksheet_service)}")
        
        print("✅ Testing optimization service import...")
        from app.services.optimization_service import optimization_service
        print(f"   Optimization service imported successfully: {type(optimization_service)}")
        
        print("✅ Testing optimization API endpoints import...")
        from app.api.v1.endpoints.optimization import router
        print(f"   Optimization API router imported successfully: {type(router)}")
        
        print("\n🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"\n❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False


def test_optimization_service_methods():
    """Test that optimization service methods are accessible"""
    
    print("\n🔧 Testing Optimization Service Methods")
    print("=" * 40)
    
    try:
        from app.services.optimization_service import optimization_service
        
        # Test that key methods exist
        methods_to_test = [
            '_extract_services_from_architecture',
            '_generate_service_alternatives',
            '_analyze_service_alternatives',
            '_generate_optimization_recommendation',
            'optimize_architecture'
        ]
        
        for method_name in methods_to_test:
            if hasattr(optimization_service, method_name):
                print(f"✅ Method '{method_name}' exists")
            else:
                print(f"❌ Method '{method_name}' missing")
                return False
        
        print("\n🎉 All optimization service methods are accessible!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error testing optimization service methods: {e}")
        return False


def test_ml_service_methods():
    """Test that ML service methods used by optimization are accessible"""
    
    print("\n🤖 Testing ML Service Methods")
    print("=" * 30)
    
    try:
        from app.services.ml_service import ml_service
        
        # Test that key methods exist
        methods_to_test = [
            'predict_lambda_cost',
            'predict_s3_cost',
            'predict_dynamodb_cost',
            'predict_elasticache_cost'
        ]
        
        for method_name in methods_to_test:
            if hasattr(ml_service, method_name):
                print(f"✅ Method '{method_name}' exists")
            else:
                print(f"❌ Method '{method_name}' missing")
                return False
        
        print("\n🎉 All ML service methods are accessible!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error testing ML service methods: {e}")
        return False


def main():
    """Run all tests"""
    
    print("🚀 Starting Import Tests for Optimization Feature")
    print("=" * 60)
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test optimization service methods
    if not test_optimization_service_methods():
        success = False
    
    # Test ML service methods
    if not test_ml_service_methods():
        success = False
    
    if success:
        print("\n✅ All tests passed! Optimization feature is ready.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)

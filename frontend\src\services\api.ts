import axios from 'axios'
import type {
  ApiResponse,
  LambdaCostRequest,
  LambdaCostResponse,
  S3CostRequest,
  S3CostResponse,
  DynamoDBCostRequest,
  DynamoDBCostResponse,
  ArchitectureCostRequest,
  ArchitectureCostResponse,
  RAGQueryRequest,
  RAGQueryResponse,
  DocumentUploadResponse,
  RAGArchitectureRequest,
  RAGArchitectureResponse,
  ChatMessageRequest,
  ChatMessageResponse
} from '@/types/api'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for authentication and logging
api.interceptors.request.use(
  (config) => {
    // Add authentication token if available
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for error handling and token refresh
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    // Handle 401 Unauthorized errors (token expired)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      // Try to refresh the token
      const refreshToken = localStorage.getItem('refresh_token')
      if (refreshToken) {
        try {
          const response = await axios.post('/api/v1/auth/refresh', {
            refresh_token: refreshToken
          })

          const { access_token, refresh_token: newRefreshToken } = response.data
          localStorage.setItem('access_token', access_token)
          localStorage.setItem('refresh_token', newRefreshToken)

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`
          return api(originalRequest)
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      } else {
        // No refresh token, redirect to login
        window.location.href = '/login'
      }
    }

    console.error('API Response Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// ML Prediction API calls
export const mlApi = {
  predictLambdaCost: async (data: LambdaCostRequest): Promise<LambdaCostResponse> => {
    const response = await api.post<LambdaCostResponse>('/ml/predict-lambda-cost', data)
    return response.data
  },

  predictS3Cost: async (data: S3CostRequest): Promise<S3CostResponse> => {
    const response = await api.post<S3CostResponse>('/ml/predict-s3-cost', data)
    return response.data
  },

  predictDynamoDBCost: async (data: DynamoDBCostRequest): Promise<DynamoDBCostResponse> => {
    const response = await api.post<DynamoDBCostResponse>('/ml/predict-dynamodb-cost', data)
    return response.data
  },

  predictAPIGatewayCost: async (data: APIGatewayCostRequest): Promise<APIGatewayCostResponse> => {
    const response = await api.post<APIGatewayCostResponse>('/ml/predict-apigateway-cost', data)
    return response.data
  },

  predictEC2Cost: async (data: EC2CostRequest): Promise<EC2CostResponse> => {
    const response = await api.post<EC2CostResponse>('/ml/predict-ec2-cost', data)
    return response.data
  },

  calculateArchitectureCost: async (data: ArchitectureCostRequest): Promise<ArchitectureCostResponse> => {
    const response = await api.post<ArchitectureCostResponse>('/ml/calculate-cost', data)
    return response.data
  },

  predictS3TransferCost: async (dataSizeGb: number, transferType: string = 'out'): Promise<ApiResponse> => {
    const response = await api.post<ApiResponse>('/ml/predict-s3-transfer-cost', null, {
      params: { data_size_gb: dataSizeGb, transfer_type: transferType }
    })
    return response.data
  },

  getWorksheetValues: async (): Promise<ApiResponse> => {
    const response = await api.get<ApiResponse>('/ml/worksheet-values')
    return response.data
  },

  getServiceWorksheetValues: async (serviceId: string): Promise<ApiResponse> => {
    const response = await api.get<ApiResponse>(`/ml/worksheet-values/${serviceId}`)
    return response.data
  },

  estimateDynamoDBCapacity: async (itemsPerSecond: number, itemSizeKb: number, operationType: string = 'write'): Promise<ApiResponse> => {
    const response = await api.post<ApiResponse>('/ml/estimate-dynamodb-capacity', null, {
      params: { items_per_second: itemsPerSecond, item_size_kb: itemSizeKb, operation_type: operationType }
    })
    return response.data
  },

  getMLServiceStatus: async (): Promise<ApiResponse> => {
    const response = await api.get<ApiResponse>('/ml/ml-service-status')
    return response.data
  },
}

// RAG API calls
export const ragApi = {
  uploadDocument: async (file: File, chunkSize: number = 500, chunkOverlap: number = 50): Promise<DocumentUploadResponse> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('chunk_size', chunkSize.toString())
    formData.append('chunk_overlap', chunkOverlap.toString())

    const response = await api.post<DocumentUploadResponse>('/rag/upload-document', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  queryDocuments: async (data: RAGQueryRequest): Promise<RAGQueryResponse> => {
    const response = await api.post<RAGQueryResponse>('/rag/query', data)
    return response.data
  },

  suggestRAGArchitecture: async (data: RAGArchitectureRequest): Promise<RAGArchitectureResponse> => {
    const response = await api.post<RAGArchitectureResponse>('/rag/suggest-architecture', data)
    return response.data
  },

  getDocumentsStatus: async (): Promise<ApiResponse> => {
    const response = await api.get<ApiResponse>('/rag/documents/status')
    return response.data
  },

  clearDocuments: async (): Promise<ApiResponse> => {
    const response = await api.delete<ApiResponse>('/rag/documents/clear')
    return response.data
  },

  getRAGHealth: async (): Promise<ApiResponse> => {
    const response = await api.get<ApiResponse>('/rag/health')
    return response.data
  },
}

// Chat API calls
export const chatApi = {
  sendMessage: async (data: ChatMessageRequest): Promise<ChatMessageResponse> => {
    const response = await api.post<ChatMessageResponse>('/chat/message', data)
    return response.data
  },

  getConversationHistory: async (conversationId: string): Promise<ApiResponse> => {
    const response = await api.get<ApiResponse>(`/chat/conversation/${conversationId}/history`)
    return response.data
  },

  clearConversationHistory: async (conversationId: string): Promise<ApiResponse> => {
    const response = await api.delete<ApiResponse>(`/chat/conversation/${conversationId}/history`)
    return response.data
  },

  clearAllConversations: async (): Promise<ApiResponse> => {
    const response = await api.delete<ApiResponse>('/chat/conversations/clear-all')
    return response.data
  },

  getActiveConversations: async (): Promise<ApiResponse> => {
    const response = await api.get<ApiResponse>('/chat/conversations/active')
    return response.data
  },

  getChatHealth: async (): Promise<ApiResponse> => {
    const response = await api.get<ApiResponse>('/chat/health')
    return response.data
  },
}

// Requirements API calls
export const requirementsApi = {
  uploadFile: async (file: File): Promise<RequirementAnalysisResponse> => {
    const formData = new FormData()
    formData.append('file', file)

    const response = await api.post<RequirementAnalysisResponse>('/requirements/upload-file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  analyzeContent: async (data: RequirementAnalysisRequest): Promise<RequirementAnalysisResponse> => {
    const response = await api.post<RequirementAnalysisResponse>('/requirements/analyze-content', data)
    return response.data
  },

  generateArchitecture: async (data: ArchitectureGenerationRequest): Promise<ArchitectureGenerationResponse> => {
    const response = await api.post<ArchitectureGenerationResponse>('/requirements/generate-architecture', data)
    return response.data
  },

  getCategories: async (): Promise<{ categories: string[], count: number }> => {
    const response = await api.get('/requirements/categories')
    return response.data.data
  },

  healthCheck: async (): Promise<any> => {
    const response = await api.get('/requirements/health')
    return response.data
  },
}

// VPC API calls
export const vpcApi = {
  createVPC: async (data: import('@/types/api').VPCCreateRequest): Promise<import('@/types/api').VPCResponse> => {
    const response = await api.post<import('@/types/api').VPCResponse>('/vpc/create', data)
    return response.data
  },

  getVPC: async (vpcId: number): Promise<import('@/types/api').VPCResponse> => {
    const response = await api.get<import('@/types/api').VPCResponse>(`/vpc/${vpcId}`)
    return response.data
  },

  updateVPC: async (vpcId: number, data: import('@/types/api').VPCUpdateRequest): Promise<import('@/types/api').VPCResponse> => {
    const response = await api.put<import('@/types/api').VPCResponse>(`/vpc/${vpcId}`, data)
    return response.data
  },

  deleteVPC: async (vpcId: number): Promise<{ success: boolean; message: string }> => {
    const response = await api.delete<{ success: boolean; message: string }>(`/vpc/${vpcId}`)
    return response.data
  },

  listVPCs: async (): Promise<import('@/types/api').VPCListResponse> => {
    const response = await api.get<import('@/types/api').VPCListResponse>('/vpc/')
    return response.data
  },

  updateVPCServices: async (vpcId: number, data: import('@/types/api').VPCServiceUpdateRequest): Promise<import('@/types/api').VPCResponse> => {
    const response = await api.put<import('@/types/api').VPCResponse>(`/vpc/${vpcId}/services`, data)
    return response.data
  },

  getVPCCost: async (vpcId: number): Promise<import('@/types/api').VPCCostResponse> => {
    const response = await api.get<import('@/types/api').VPCCostResponse>(`/vpc/${vpcId}/cost`)
    return response.data
  },

  calculateVPCBoundary: async (vpcId: number, nodes: any[]): Promise<import('@/types/api').VPCBoundaryResponse> => {
    const response = await api.post<import('@/types/api').VPCBoundaryResponse>(`/vpc/${vpcId}/boundary`, { vpc_id: vpcId, nodes })
    return response.data
  },

  getVPCByService: async (serviceId: string): Promise<import('@/types/api').VPCResponse | null> => {
    const response = await api.get<import('@/types/api').VPCResponse | null>(`/vpc/service/${serviceId}`)
    return response.data
  },

  getTotalVPCCosts: async (): Promise<{ total_vpc_cost: number }> => {
    const response = await api.get<{ total_vpc_cost: number }>('/vpc/costs/total')
    return response.data
  },

  getServiceVPCMapping: async (): Promise<{ service_vpc_mapping: Record<string, number> }> => {
    const response = await api.get<{ service_vpc_mapping: Record<string, number> }>('/vpc/mapping/services')
    return response.data
  },

  clearAllVPCs: async (): Promise<{ success: boolean; message: string }> => {
    const response = await api.delete<{ success: boolean; message: string }>('/vpc/')
    return response.data
  },
}

// Authentication API calls
export const authApi = {
  login: async (username: string, password: string) => {
    const formData = new FormData()
    formData.append('username', username)
    formData.append('password', password)

    const response = await api.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  register: async (userData: {
    username: string
    password: string
    email?: string
    full_name?: string
  }) => {
    const response = await api.post('/auth/register', userData)
    return response.data
  },

  refreshToken: async (refreshToken: string) => {
    const response = await api.post('/auth/refresh', {
      refresh_token: refreshToken
    })
    return response.data
  },

  logout: async (refreshToken: string) => {
    const response = await api.post('/auth/logout', {
      refresh_token: refreshToken
    })
    return response.data
  },

  getCurrentUser: async () => {
    const response = await api.get('/auth/me')
    return response.data
  },

  updateProfile: async (userData: {
    email?: string
    full_name?: string
    password?: string
  }) => {
    const response = await api.put('/auth/me', userData)
    return response.data
  },
}

// Architecture Database API calls
export const architectureApi = {
  saveToDatabase: async (architectureData: any) => {
    const response = await api.post('/architecture/save-to-database', architectureData)
    return response.data
  },

  listUserArchitectures: async () => {
    const response = await api.get('/architecture/list')
    return response.data
  },

  loadFromDatabase: async (architectureId: number) => {
    const response = await api.get(`/architecture/load-from-database/${architectureId}`)
    return response.data
  },

  deleteFromDatabase: async (architectureId: number) => {
    const response = await api.delete(`/architecture/delete-from-database/${architectureId}`)
    return response.data
  },

  exportAsJson: async (architectureData: any) => {
    const response = await api.post('/architecture/export-json', architectureData)
    return response.data
  },

  exportAsPickle: async (architectureData: any) => {
    const response = await api.post('/architecture/export-pickle', architectureData, {
      responseType: 'blob'
    })
    return response.data
  },
}

// Admin API calls
export const adminApi = {
  getUsers: async (params?: {
    skip?: number
    limit?: number
    search?: string
    is_active?: boolean
    is_superuser?: boolean
  }) => {
    const response = await api.get('/admin/users', { params })
    return response.data
  },

  getSystemStats: async () => {
    const response = await api.get('/admin/stats')
    return response.data
  },

  getUserDetails: async (userId: number) => {
    const response = await api.get(`/admin/users/${userId}`)
    return response.data
  },

  updateUser: async (userId: number, userData: {
    email?: string
    full_name?: string
    password?: string
  }) => {
    const response = await api.put(`/admin/users/${userId}`, userData)
    return response.data
  },

  toggleUserActive: async (userId: number) => {
    const response = await api.post(`/admin/users/${userId}/toggle-active`)
    return response.data
  },

  revokeUserSessions: async (userId: number) => {
    const response = await api.post(`/admin/users/${userId}/revoke-sessions`)
    return response.data
  },

  deleteUser: async (userId: number) => {
    const response = await api.delete(`/admin/users/${userId}`)
    return response.data
  },

  createUser: async (userData: {
    username: string
    password: string
    email?: string
    full_name?: string
  }) => {
    const response = await api.post('/admin/users', userData)
    return response.data
  },
}

// General API calls
export const generalApi = {
  getHealth: async (): Promise<ApiResponse> => {
    const response = await api.get<ApiResponse>('/')
    return response.data
  },
}

export default api

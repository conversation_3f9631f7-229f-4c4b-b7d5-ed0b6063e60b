import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { Calculator, DollarSign, Clock, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react'
import { Architecture, CostAnalysis } from '@/types/architecture'
import { mlApi } from '@/services/api'
import { ArchitectureCostRequest } from '@/types/api'
import { useToast } from '@/hooks/use-toast'

interface CostCalculationPanelProps {
  architecture: Architecture
  onCostCalculated?: (analysis: CostAnalysis) => void
}

export const CostCalculationPanel: React.FC<CostCalculationPanelProps> = ({
  architecture,
  onCostCalculated
}) => {
  const [isCalculating, setIsCalculating] = useState(false)
  const [costAnalysis, setCostAnalysis] = useState<CostAnalysis | null>(null)
  const { toast } = useToast()

  const handleCalculateCost = async () => {
    if (architecture.nodes.length === 0) {
      toast({
        title: "No Services",
        description: "Please add some services to your architecture before calculating costs.",
        variant: "destructive"
      })
      return
    }

    setIsCalculating(true)

    try {
      // Prepare request data for the ML API - match backend ArchitectureCostRequest format
      const request: ArchitectureCostRequest = {
        architecture_type: architecture.metadata?.archType === 'Microservices' ? 'Microservices' : 'Monolith',
        nodes: architecture.nodes.map(node => ({
          id: node.id,
          service_id: node.data.service?.id || node.id,
          service_name: node.data.service?.name || node.data.label,
          config: node.data.config || {},
          position: node.position
        })),
        edges: architecture.edges.map(edge => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          config: edge.data?.config || {}
        })),
        architecture: {
          name: architecture.name,
          description: architecture.description,
          uses_vpc: architecture.metadata?.usesVpc || false,
          nat_gateways: architecture.metadata?.natGateways || 0
        }
      }

      const response = await mlApi.calculateArchitectureCost(request)

      // Convert API response to CostAnalysis format
      const analysis: CostAnalysis = {
        totalMonthlyCost: response.total_cost,
        totalLatency: response.total_latency,
        breakdown: response.service_breakdown.map(service => ({
          serviceName: service.service_name,
          cost: service.cost,
          latency: service.latency,
          percentage: (service.cost / response.total_cost) * 100
        })),
        recommendations: response.recommendations?.map(rec => ({
          type: 'Cost Optimization',
          message: rec,
          impact: 'medium'
        })) || []
      }

      setCostAnalysis(analysis)
      onCostCalculated?.(analysis)

      toast({
        title: "Cost Calculated",
        description: `Total monthly cost: $${analysis.totalMonthlyCost.toFixed(2)}`,
        variant: "default"
      })
    } catch (error) {
      console.error('Error calculating cost:', error)
      toast({
        title: "Calculation Error",
        description: "Failed to calculate costs. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsCalculating(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  const formatLatency = (latency: number) => {
    return `${latency.toFixed(2)}ms`
  }

  const getRecommendationIcon = (impact: string) => {
    switch (impact) {
      case 'high': return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'medium': return <TrendingUp className="h-4 w-4 text-yellow-500" />
      case 'low': return <CheckCircle className="h-4 w-4 text-green-500" />
      default: return <CheckCircle className="h-4 w-4 text-blue-500" />
    }
  }

  const getRecommendationColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'destructive'
      case 'medium': return 'secondary'
      case 'low': return 'default'
      default: return 'outline'
    }
  }

  return (
    <div className="space-y-4">
      {/* Calculate Button */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Cost & Performance Analysis
          </CardTitle>
          <CardDescription>
            Calculate the estimated cost per request and latency for your architecture
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={handleCalculateCost}
            disabled={isCalculating || architecture.nodes.length === 0}
            className="w-full"
            size="lg"
          >
            {isCalculating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Calculating...
              </>
            ) : (
              <>
                <Calculator className="h-4 w-4 mr-2" />
                Compute Cost & Latency
              </>
            )}
          </Button>

          {architecture.nodes.length === 0 && (
            <p className="text-sm text-muted-foreground mt-2 text-center">
              Add services to your architecture to enable cost calculation
            </p>
          )}
        </CardContent>
      </Card>



      {/* Cost Analysis Results */}
      {costAnalysis && (
        <div className="space-y-4">
          {/* Summary Cards */}
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Cost</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(costAnalysis.totalMonthlyCost)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Avg Latency</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {formatLatency(costAnalysis.totalLatency)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Cost Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Cost Breakdown by Service</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {costAnalysis.breakdown.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{item.serviceName}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {formatLatency(item.latency)}
                      </span>
                      <span className="font-semibold">
                        {formatCurrency(item.cost)}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={item.percentage} className="flex-1" />
                    <span className="text-xs text-muted-foreground w-12">
                      {item.percentage.toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Recommendations */}
          {costAnalysis.recommendations && costAnalysis.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Optimization Recommendations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {costAnalysis.recommendations.map((rec, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                    {getRecommendationIcon(rec.impact)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant={getRecommendationColor(rec.impact) as any}>
                          {rec.type}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {rec.impact} impact
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{rec.message}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Architecture Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Architecture Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Total Services:</span>
                  <span className="ml-2">{architecture.nodes.length}</span>
                </div>
                <div>
                  <span className="font-medium">Connections:</span>
                  <span className="ml-2">{architecture.edges.length}</span>
                </div>
                <div>
                  <span className="font-medium">Architecture Type:</span>
                  <span className="ml-2">{architecture.metadata?.archType || 'Custom'}</span>
                </div>
                <div>
                  <span className="font-medium">Primary Provider:</span>
                  <span className="ml-2">{architecture.metadata?.provider || 'Multi-Cloud'}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

export default CostCalculationPanel

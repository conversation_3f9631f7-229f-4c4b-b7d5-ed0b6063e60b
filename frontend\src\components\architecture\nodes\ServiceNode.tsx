import React, { memo } from 'react'
import { <PERSON>le, Position, NodeProps } from 'reactflow'
import { CloudService } from '@/types/architecture'
import ServiceIcon from '@/components/ui/ServiceIcon'
import { cn } from '@/lib/utils'
import { CheckCircle, Zap, Settings } from 'lucide-react'

interface ServiceNodeData {
  service: CloudService
  config: Record<string, any>
  cost?: number
  latency?: number
  label: string
  selected?: boolean
  optimization?: {
    applied: boolean
    timestamp: string
    action: string
    originalService: string
    recommendedService: string
    justification: string
    configurationChanges?: string
    noChangesNeeded?: boolean
  }
}

export const ServiceNode = memo<NodeProps<ServiceNodeData>>(({ data, selected }) => {
  const { service, optimization } = data

  const getProviderBorderColor = (provider: string) => {
    // If optimized, use green border
    if (optimization?.applied) {
      return 'border-green-500'
    }

    switch (provider) {
      case 'AWS': return 'border-orange-400'
      case 'GCP': return 'border-blue-400'
      case 'Azure': return 'border-blue-600'
      default: return 'border-gray-400'
    }
  }

  const getProviderBgColor = (provider: string) => {
    // If optimized, use green background
    if (optimization?.applied) {
      return 'bg-green-50 hover:bg-green-100'
    }

    switch (provider) {
      case 'AWS': return 'bg-orange-50 hover:bg-orange-100'
      case 'GCP': return 'bg-blue-50 hover:bg-blue-100'
      case 'Azure': return 'bg-blue-50 hover:bg-blue-100'
      default: return 'bg-gray-50 hover:bg-gray-100'
    }
  }

  const getOptimizationIcon = () => {
    if (!optimization?.applied) return null

    switch (optimization.action) {
      case 'replace_service':
        return <Zap className="w-3 h-3 text-green-600" />
      case 'optimize_configuration':
        return <Settings className="w-3 h-3 text-green-600" />
      case 'keep_current':
        return <CheckCircle className="w-3 h-3 text-green-600" />
      default:
        return <CheckCircle className="w-3 h-3 text-green-600" />
    }
  }

  const getOptimizationTooltip = () => {
    if (!optimization?.applied) return service.name

    const actionText = optimization.action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
    const timestamp = new Date(optimization.timestamp).toLocaleString()

    return `${service.name}\n\nOptimized: ${actionText}\nApplied: ${timestamp}\n\n${optimization.justification}`
  }

  return (
    <>
      {/* Top Handle - Input */}
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        className="w-2 h-2 !bg-gray-400 border border-white"
        style={{ top: -4 }}
      />

      {/* Compact Icon-Only Node */}
      <div className={cn(
        'w-12 h-12 rounded-lg transition-all duration-200 cursor-pointer flex items-center justify-center border-2 shadow-sm hover:shadow-md',
        getProviderBorderColor(service.provider),
        getProviderBgColor(service.provider),
        selected ? 'ring-2 ring-blue-500 shadow-md scale-110' : 'hover:scale-105'
      )}>
        <div className="relative group">
          <ServiceIcon
            icon={service.icon}
            fallbackIcon={service.fallbackIcon}
            alt={service.name}
            size="md"
            className="drop-shadow-sm"
          />

          {/* Optimization indicator */}
          {optimization?.applied && (
            <div className="absolute -top-1 -right-1 bg-green-500 rounded-full p-0.5 shadow-sm">
              {getOptimizationIcon()}
            </div>
          )}

          {/* Enhanced tooltip on hover */}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-pre-line z-50 pointer-events-none max-w-xs text-center">
            {getOptimizationTooltip()}
          </div>
        </div>
      </div>

      {/* Bottom Handle - Output */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        className="w-2 h-2 !bg-gray-400 border border-white"
        style={{ bottom: -4 }}
      />

      {/* Right Handle - Output */}
      <Handle
        type="source"
        position={Position.Right}
        id="right"
        className="w-2 h-2 !bg-gray-400 border border-white"
        style={{ right: -4 }}
      />

      {/* Left Handle - Input */}
      <Handle
        type="target"
        position={Position.Left}
        id="left"
        className="w-2 h-2 !bg-gray-400 border border-white"
        style={{ left: -4 }}
      />
    </>
  )
})

ServiceNode.displayName = 'ServiceNode'

// Specialized node components for different providers
export const AWSServiceNode = memo<NodeProps<ServiceNodeData>>((props) => (
  <ServiceNode {...props} />
))

export const GCPServiceNode = memo<NodeProps<ServiceNodeData>>((props) => (
  <ServiceNode {...props} />
))

export const AzureServiceNode = memo<NodeProps<ServiceNodeData>>((props) => (
  <ServiceNode {...props} />
))

AWSServiceNode.displayName = 'AWSServiceNode'
GCPServiceNode.displayName = 'GCPServiceNode'
AzureServiceNode.displayName = 'AzureServiceNode'



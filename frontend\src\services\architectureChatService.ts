/**
 * Architecture Chat Service
 * 
 * API service for AI-powered architecture chat functionality
 */

import api from './api'
import type { ArchitectureNode, ArchitectureEdge } from '@/types/architecture'

// Request Types
export interface ArchitectureChatRequest {
  user_query: string
  architecture_data?: {
    nodes: ArchitectureNode[]
    edges: ArchitectureEdge[]
  }
  conversation_id?: string
}

export interface ArchitectureInsightsRequest {
  architecture_data: {
    nodes: ArchitectureNode[]
    edges: ArchitectureEdge[]
  }
}

export interface ArchitectureImprovementsRequest {
  architecture_data: {
    nodes: ArchitectureNode[]
    edges: ArchitectureEdge[]
  }
  focus_area: string
}

// Response Types
export interface ArchitectureChatResponse {
  status: string
  response: string
  conversation_id: string
  timestamp: string
  tools_used: string[]
  context_provided: boolean
  error?: string
}

export interface ArchitectureInsightsResponse {
  status: string
  insights: string
  timestamp: string
  error?: string
}

export interface ConversationHistoryResponse {
  status: string
  history: Array<{
    role: string
    content: string
    timestamp: string
  }>
  conversation_id: string
  error?: string
}

export interface HealthCheckResponse {
  status: string
  service: string
  tools_available: number
  llm_initialized: boolean
  agent_initialized: boolean
}

class ArchitectureChatService {
  private baseUrl = '/architecture-chat'

  /**
   * Chat with AI about architecture
   */
  async chatWithArchitecture(request: ArchitectureChatRequest): Promise<ArchitectureChatResponse> {
    try {
      console.log('Sending chat request:', request)
      
      const response = await api.post(`${this.baseUrl}/chat`, request)
      
      console.log('Chat response:', response.data)
      return response.data
    } catch (error: any) {
      console.error('Architecture chat error:', error)
      
      // Handle different error types
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail)
      } else if (error.response?.status === 503) {
        throw new Error('Architecture chat service is currently unavailable. Please try again later.')
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required. Please log in.')
      } else {
        throw new Error('Failed to communicate with AI assistant. Please check your connection and try again.')
      }
    }
  }

  /**
   * Get comprehensive architecture insights
   */
  async getArchitectureInsights(request: ArchitectureInsightsRequest): Promise<ArchitectureInsightsResponse> {
    try {
      console.log('Getting architecture insights:', request)
      
      const response = await api.post(`${this.baseUrl}/insights`, request)
      
      console.log('Insights response:', response.data)
      return response.data
    } catch (error: any) {
      console.error('Architecture insights error:', error)
      
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail)
      } else {
        throw new Error('Failed to get architecture insights. Please try again.')
      }
    }
  }

  /**
   * Get architecture improvement suggestions
   */
  async getArchitectureImprovements(request: ArchitectureImprovementsRequest): Promise<ArchitectureChatResponse> {
    try {
      console.log('Getting architecture improvements:', request)
      
      const response = await api.post(`${this.baseUrl}/improvements`, request)
      
      console.log('Improvements response:', response.data)
      return response.data
    } catch (error: any) {
      console.error('Architecture improvements error:', error)
      
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail)
      } else {
        throw new Error('Failed to get improvement suggestions. Please try again.')
      }
    }
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(conversationId: string): Promise<ConversationHistoryResponse> {
    try {
      console.log('Getting conversation history for:', conversationId)
      
      const response = await api.get(`${this.baseUrl}/history/${conversationId}`)
      
      console.log('History response:', response.data)
      return response.data
    } catch (error: any) {
      console.error('Conversation history error:', error)
      
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail)
      } else {
        throw new Error('Failed to get conversation history. Please try again.')
      }
    }
  }

  /**
   * Clear conversation history
   */
  async clearConversationHistory(): Promise<{ status: string; message: string }> {
    try {
      console.log('Clearing conversation history')
      
      const response = await api.delete(`${this.baseUrl}/history`)
      
      console.log('Clear history response:', response.data)
      return response.data
    } catch (error: any) {
      console.error('Clear history error:', error)
      
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail)
      } else {
        throw new Error('Failed to clear conversation history. Please try again.')
      }
    }
  }

  /**
   * Check service health
   */
  async checkHealth(): Promise<HealthCheckResponse> {
    try {
      console.log('Checking architecture chat service health')
      
      const response = await api.get(`${this.baseUrl}/health`)
      
      console.log('Health check response:', response.data)
      return response.data
    } catch (error: any) {
      console.error('Health check error:', error)
      
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail)
      } else {
        throw new Error('Failed to check service health.')
      }
    }
  }

  /**
   * Test connection to the service
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.checkHealth()
      return true
    } catch (error) {
      console.error('Connection test failed:', error)
      return false
    }
  }

  /**
   * Quick architecture analysis
   */
  async quickAnalysis(nodes: ArchitectureNode[], edges: ArchitectureEdge[]): Promise<string> {
    try {
      const response = await this.chatWithArchitecture({
        user_query: "Please provide a quick analysis of this architecture including service count, patterns, and any immediate observations.",
        architecture_data: { nodes, edges }
      })
      
      return response.response
    } catch (error) {
      console.error('Quick analysis error:', error)
      throw error
    }
  }

  /**
   * Quick cost estimate
   */
  async quickCostEstimate(nodes: ArchitectureNode[], edges: ArchitectureEdge[]): Promise<string> {
    try {
      const response = await this.chatWithArchitecture({
        user_query: "Please calculate the estimated cost of this architecture and provide a breakdown by service.",
        architecture_data: { nodes, edges }
      })
      
      return response.response
    } catch (error) {
      console.error('Quick cost estimate error:', error)
      throw error
    }
  }

  /**
   * Quick optimization suggestions
   */
  async quickOptimization(nodes: ArchitectureNode[], edges: ArchitectureEdge[]): Promise<string> {
    try {
      const response = await this.chatWithArchitecture({
        user_query: "Please suggest 3-5 specific optimizations for this architecture focusing on cost and performance.",
        architecture_data: { nodes, edges }
      })
      
      return response.response
    } catch (error) {
      console.error('Quick optimization error:', error)
      throw error
    }
  }

  /**
   * Search for AWS best practices
   */
  async searchBestPractices(topic: string): Promise<string> {
    try {
      const response = await this.chatWithArchitecture({
        user_query: `Search for current AWS best practices related to: ${topic}. Please provide up-to-date information and actionable recommendations.`
      })
      
      return response.response
    } catch (error) {
      console.error('Best practices search error:', error)
      throw error
    }
  }
}

// Export singleton instance
export const architectureChatApi = new ArchitectureChatService()
export default architectureChatApi
